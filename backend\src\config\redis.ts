import { createClient, RedisClientType } from 'redis';

let redisClient: RedisClientType | null = null;

// 检查是否启用Redis
export function isRedisEnabled(): boolean {
  return process.env.ENABLE_REDIS === 'true';
}

export async function connectRedis(): Promise<RedisClientType | null> {
  // 如果未启用Redis，直接返回null
  if (!isRedisEnabled()) {
    console.log('📝 Redis已禁用，跳过连接');
    return null;
  }

  try {
    console.log('🔄 正在连接Redis...');
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

    redisClient = createClient({
      url: redisUrl,
      password: process.env.REDIS_PASSWORD || undefined,
    });

    redisClient.on('error', (error) => {
      console.error('Redis connection error:', error);
    });

    redisClient.on('connect', () => {
      console.log('✅ Redis connected');
    });

    redisClient.on('reconnecting', () => {
      console.log('🔄 Redis reconnecting');
    });

    redisClient.on('ready', () => {
      console.log('✅ Redis ready');
    });

    await redisClient.connect();

    return redisClient;
  } catch (error) {
    console.error('❌ Redis connection failed:', error);
    console.log('💡 提示: 如果不需要Redis，请在.env中设置 ENABLE_REDIS=false');
    throw error;
  }
}

export function getRedisClient(): RedisClientType | null {
  if (!isRedisEnabled()) {
    return null;
  }

  if (!redisClient) {
    throw new Error('Redis client not initialized. Call connectRedis() first.');
  }
  return redisClient;
}

export async function disconnectRedis(): Promise<void> {
  if (!isRedisEnabled() || !redisClient) {
    return;
  }

  try {
    await redisClient.quit();
    console.log('✅ Redis disconnected successfully');
    redisClient = null;
  } catch (error) {
    console.error('❌ Error disconnecting from Redis:', error);
    throw error;
  }
}
