{"name": "d2x-frontend", "version": "1.0.0", "description": "前端Vue3应用", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "quill": "^1.3.7", "katex": "^0.16.8", "quill-image-uploader": "^1.3.0", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "sortablejs": "^1.15.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/node": "^20.10.4", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/sortablejs": "^1.15.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "sass": "^1.69.5", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}