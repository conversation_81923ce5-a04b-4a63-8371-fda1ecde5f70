# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
# MySQL
DB_HOST=**************
DB_PORT=3306
DB_NAME=d2x_db
DB_USER=root
DB_PASSWORD=1qaz!@#$lymysql

# Redis配置
ENABLE_REDIS=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=ly.x-jwt-061
JWT_EXPIRES_IN=1d

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=50MB

# 第三方API配置
# Mathpix API
MATHPIX_APP_ID=your-mathpix-app-id
MATHPIX_APP_KEY=your-mathpix-app-key

# 邮件配置（邮箱验证必需）
SMTP_HOST=smtp.163.com
SMTP_PORT=465
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=UGsRA36hd7UGjBKw
SMTP_FROM=<EMAIL>

# 前端URL（用于邮件链接）
FRONTEND_URL=http://localhost:5173

# 支付配置（可选）
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# 云存储配置（可选）
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name
