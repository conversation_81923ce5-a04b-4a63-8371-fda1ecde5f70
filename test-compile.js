#!/usr/bin/env node

/**
 * 简单的TypeScript编译测试脚本
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🔍 测试TypeScript编译...');

// 测试编译
const tsc = spawn('npx', ['tsc', '--noEmit'], {
  cwd: path.join(__dirname, 'backend'),
  stdio: 'inherit',
  shell: true
});

tsc.on('error', (error) => {
  console.error('❌ 编译测试失败:', error);
  process.exit(1);
});

tsc.on('close', (code) => {
  if (code === 0) {
    console.log('✅ TypeScript编译成功！');
    console.log('🎉 项目可以正常启动');
  } else {
    console.log(`❌ 编译失败，退出代码: ${code}`);
  }
  process.exit(code);
});
