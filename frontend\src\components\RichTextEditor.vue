<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <!-- 自定义工具栏 -->
      <div class="toolbar-group">
        <button class="toolbar-btn" @click="insertFormula" title="插入公式">
          <el-icon><Operation /></el-icon>
          公式
        </button>
        <button class="toolbar-btn" @click="insertImage" title="插入图片">
          <el-icon><Picture /></el-icon>
          图片
        </button>
        <button class="toolbar-btn" @click="insertTable" title="插入表格">
          <el-icon><Grid /></el-icon>
          表格
        </button>
      </div>
    </div>
    
    <!-- Quill编辑器容器 -->
    <div ref="editorContainer" class="editor-container"></div>
    
    <!-- 公式编辑对话框 -->
    <el-dialog v-model="formulaDialogVisible" title="插入/编辑公式" width="600px">
      <div class="formula-editor">
        <div class="formula-input">
          <el-input
            v-model="formulaLatex"
            type="textarea"
            :rows="4"
            placeholder="请输入LaTeX公式，例如：E = mc^2"
            @input="updateFormulaPreview"
          />
        </div>
        <div class="formula-preview">
          <h4>预览：</h4>
          <div ref="formulaPreview" class="preview-content"></div>
        </div>
        <div class="formula-examples">
          <h4>常用公式示例：</h4>
          <div class="examples-grid">
            <button 
              v-for="example in formulaExamples" 
              :key="example.name"
              class="example-btn"
              @click="useFormulaExample(example.latex)"
            >
              {{ example.name }}
            </button>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="formulaDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="insertFormulaToEditor">插入公式</el-button>
      </template>
    </el-dialog>

    <!-- 图片上传对话框 -->
    <el-dialog v-model="imageDialogVisible" title="插入图片" width="500px">
      <div class="image-upload">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeImageUpload"
          :on-success="handleImageSuccess"
          :on-error="handleImageError"
          :show-file-list="false"
          accept="image/*"
          drag
        >
          <el-icon class="upload-icon"><UploadFilled /></el-icon>
          <div class="upload-text">
            <p>点击或拖拽图片到此处上传</p>
            <p class="upload-tip">支持 JPG、PNG、GIF 格式，大小不超过 5MB</p>
          </div>
        </el-upload>
        
        <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
          <el-progress :percentage="uploadProgress" />
        </div>
        
        <div v-if="previewImageUrl" class="image-preview">
          <img :src="previewImageUrl" alt="预览图片" />
        </div>
      </div>
      <template #footer>
        <el-button @click="imageDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!previewImageUrl"
          @click="insertImageToEditor"
        >
          插入图片
        </el-button>
      </template>
    </el-dialog>

    <!-- 表格插入对话框 -->
    <el-dialog v-model="tableDialogVisible" title="插入表格" width="400px">
      <div class="table-config">
        <el-form :model="tableConfig" label-width="80px">
          <el-form-item label="行数">
            <el-input-number v-model="tableConfig.rows" :min="1" :max="20" />
          </el-form-item>
          <el-form-item label="列数">
            <el-input-number v-model="tableConfig.cols" :min="1" :max="10" />
          </el-form-item>
          <el-form-item label="表头">
            <el-switch v-model="tableConfig.hasHeader" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="tableDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="insertTableToEditor">插入表格</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Operation, Picture, Grid, UploadFilled } from '@element-plus/icons-vue'
import Quill from 'quill'
import katex from 'katex'
import { useAuthStore } from '@/stores/auth'

// 导入样式
import 'quill/dist/quill.snow.css'
import 'katex/dist/katex.min.css'

// Props和Emits
interface Props {
  modelValue?: string
  placeholder?: string
  readonly?: boolean
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  readonly: false,
  height: '300px'
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

// 响应式数据
const editorContainer = ref<HTMLElement>()
const formulaPreview = ref<HTMLElement>()
const uploadRef = ref()
let quillInstance: Quill | null = null

const formulaDialogVisible = ref(false)
const imageDialogVisible = ref(false)
const tableDialogVisible = ref(false)

const formulaLatex = ref('')
const previewImageUrl = ref('')
const uploadProgress = ref(0)

const tableConfig = ref({
  rows: 3,
  cols: 3,
  hasHeader: true
})

// 认证信息
const authStore = useAuthStore()
const uploadAction = import.meta.env.VITE_API_BASE_URL + '/file/upload/image'
const uploadHeaders = {
  'Authorization': `Bearer ${authStore.token}`
}

// 公式示例
const formulaExamples = [
  { name: '分数', latex: '\\frac{a}{b}' },
  { name: '根号', latex: '\\sqrt{x}' },
  { name: '上标', latex: 'x^2' },
  { name: '下标', latex: 'x_1' },
  { name: '求和', latex: '\\sum_{i=1}^{n} x_i' },
  { name: '积分', latex: '\\int_{a}^{b} f(x)dx' },
  { name: '极限', latex: '\\lim_{x \\to \\infty} f(x)' },
  { name: '矩阵', latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}' }
]

// 初始化编辑器
const initEditor = () => {
  if (!editorContainer.value) return

  // 自定义工具栏配置
  const toolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'],
    ['blockquote', 'code-block'],
    [{ 'header': 1 }, { 'header': 2 }],
    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
    [{ 'script': 'sub'}, { 'script': 'super' }],
    [{ 'indent': '-1'}, { 'indent': '+1' }],
    [{ 'direction': 'rtl' }],
    [{ 'size': ['small', false, 'large', 'huge'] }],
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    [{ 'color': [] }, { 'background': [] }],
    [{ 'font': [] }],
    [{ 'align': [] }],
    ['clean'],
    ['link']
  ]

  quillInstance = new Quill(editorContainer.value, {
    theme: 'snow',
    placeholder: props.placeholder,
    readOnly: props.readonly,
    modules: {
      toolbar: toolbarOptions
    }
  })

  // 设置编辑器高度
  const editorElement = editorContainer.value.querySelector('.ql-editor') as HTMLElement
  if (editorElement) {
    editorElement.style.minHeight = props.height
  }

  // 设置初始内容
  if (props.modelValue) {
    quillInstance.root.innerHTML = props.modelValue
  }

  // 监听内容变化
  quillInstance.on('text-change', () => {
    const html = quillInstance!.root.innerHTML
    emit('update:modelValue', html)
    emit('change', html)
  })
}

// 插入公式
const insertFormula = () => {
  formulaLatex.value = ''
  formulaDialogVisible.value = true
  nextTick(() => {
    updateFormulaPreview()
  })
}

// 更新公式预览
const updateFormulaPreview = () => {
  if (!formulaPreview.value) return
  
  try {
    if (formulaLatex.value.trim()) {
      katex.render(formulaLatex.value, formulaPreview.value, {
        displayMode: true,
        throwOnError: false
      })
    } else {
      formulaPreview.value.innerHTML = '<span class="placeholder">公式预览将在这里显示</span>'
    }
  } catch (error) {
    formulaPreview.value.innerHTML = '<span class="error">公式语法错误</span>'
  }
}

// 使用公式示例
const useFormulaExample = (latex: string) => {
  formulaLatex.value = latex
  updateFormulaPreview()
}

// 插入公式到编辑器
const insertFormulaToEditor = () => {
  if (!quillInstance || !formulaLatex.value.trim()) return

  try {
    // 渲染公式为HTML
    const formulaHtml = katex.renderToString(formulaLatex.value, {
      displayMode: false,
      throwOnError: false
    })

    // 获取当前光标位置
    const range = quillInstance.getSelection()
    const index = range ? range.index : quillInstance.getLength()

    // 插入公式HTML
    quillInstance.clipboard.dangerouslyPasteHTML(index, `<span class="formula">${formulaHtml}</span>`)

    formulaDialogVisible.value = false
    ElMessage.success('公式插入成功')
  } catch (error) {
    ElMessage.error('公式插入失败')
  }
}

// 插入图片
const insertImage = () => {
  previewImageUrl.value = ''
  uploadProgress.value = 0
  imageDialogVisible.value = true
}

// 图片上传前检查
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 图片上传成功
const handleImageSuccess = (response: any) => {
  uploadProgress.value = 100
  previewImageUrl.value = response.url
  ElMessage.success('图片上传成功')
}

// 图片上传失败
const handleImageError = () => {
  uploadProgress.value = 0
  ElMessage.error('图片上传失败')
}

// 插入图片到编辑器
const insertImageToEditor = () => {
  if (!quillInstance || !previewImageUrl.value) return

  const range = quillInstance.getSelection()
  const index = range ? range.index : quillInstance.getLength()

  quillInstance.insertEmbed(index, 'image', previewImageUrl.value)
  imageDialogVisible.value = false
  ElMessage.success('图片插入成功')
}

// 插入表格
const insertTable = () => {
  tableDialogVisible.value = true
}

// 插入表格到编辑器
const insertTableToEditor = () => {
  if (!quillInstance) return

  const { rows, cols, hasHeader } = tableConfig.value
  let tableHtml = '<table class="table-bordered">'

  for (let i = 0; i < rows; i++) {
    tableHtml += '<tr>'
    for (let j = 0; j < cols; j++) {
      const tag = hasHeader && i === 0 ? 'th' : 'td'
      tableHtml += `<${tag}>&nbsp;</${tag}>`
    }
    tableHtml += '</tr>'
  }
  tableHtml += '</table><p><br></p>'

  const range = quillInstance.getSelection()
  const index = range ? range.index : quillInstance.getLength()

  quillInstance.clipboard.dangerouslyPasteHTML(index, tableHtml)
  tableDialogVisible.value = false
  ElMessage.success('表格插入成功')
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (quillInstance && newValue !== quillInstance.root.innerHTML) {
    quillInstance.root.innerHTML = newValue || ''
  }
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initEditor()
  })
})

onBeforeUnmount(() => {
  if (quillInstance) {
    quillInstance = null
  }
})

// 暴露方法
defineExpose({
  getContent: () => quillInstance?.root.innerHTML || '',
  setContent: (content: string) => {
    if (quillInstance) {
      quillInstance.root.innerHTML = content
    }
  },
  focus: () => quillInstance?.focus(),
  blur: () => quillInstance?.blur()
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  padding: 8px 12px;
}

.toolbar-group {
  display: flex;
  gap: 8px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  color: #409eff;
  border-color: #409eff;
}

.editor-container {
  background: white;
}

.formula-editor {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formula-preview {
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #f9f9f9;
  min-height: 60px;
  display: flex;
  flex-direction: column;
}

.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content .placeholder {
  color: #909399;
  font-style: italic;
}

.preview-content .error {
  color: #f56c6c;
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.example-btn {
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: white;
  color: #606266;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.example-btn:hover {
  color: #409eff;
  border-color: #409eff;
}

.image-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.upload-text {
  text-align: center;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 4px;
}

.table-config {
  padding: 16px 0;
}

/* 全局样式 */
:deep(.ql-editor) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

:deep(.ql-editor .formula) {
  display: inline-block;
  margin: 0 2px;
  padding: 2px 4px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 3px;
}

:deep(.ql-editor .table-bordered) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

:deep(.ql-editor .table-bordered th),
:deep(.ql-editor .table-bordered td) {
  border: 1px solid #dcdfe6;
  padding: 8px 12px;
  text-align: left;
}

:deep(.ql-editor .table-bordered th) {
  background: #f5f7fa;
  font-weight: 600;
}
</style>
