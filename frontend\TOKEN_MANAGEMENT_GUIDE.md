# 🔐 Token管理和自动跳转问题解决指南

## 🚨 问题描述
用户在使用过程中经常被自动跳转到登录页面，影响用户体验。

## 🔍 问题原因分析

### 1. Token过期问题
- **默认过期时间**: 1天 (24小时)
- **问题**: 用户长时间使用应用时token会过期
- **表现**: 突然跳转到登录页面

### 2. 网络请求失败
- **401错误**: 服务器返回未授权状态
- **网络中断**: 临时网络问题导致请求失败
- **服务器重启**: 后端服务重启导致session丢失

### 3. 前端状态管理问题
- **localStorage清除**: 浏览器清理或用户手动清除
- **多标签页冲突**: 不同标签页之间的状态不同步
- **内存泄漏**: 长时间运行导致状态异常

## ✅ 解决方案

### 1. 智能Token管理
我们已经实现了以下功能：

#### Token自动检查
```typescript
// 每30秒检查token状态
const checkTokenStatus = () => {
  if (tokenManager.isTokenExpired()) {
    // 处理过期
  }
  if (tokenManager.shouldRefreshToken()) {
    // 自动刷新
  }
}
```

#### 自动刷新机制
```typescript
// 在token过期前5分钟自动刷新
const REFRESH_THRESHOLD = 5 * 60 * 1000 // 5分钟
```

#### 页面可见性检测
```typescript
// 页面重新获得焦点时检查token
document.addEventListener('visibilitychange', handleVisibilityChange)
```

### 2. 用户友好的错误处理

#### 渐进式提醒
- **正常状态**: 无提示
- **即将过期**: 警告提示
- **已过期**: 错误提示并跳转

#### 避免重复跳转
```typescript
// 只在非登录页面时跳转
if (router.currentRoute.value.path !== '/login') {
  router.push('/login')
}
```

### 3. 配置优化

#### 延长Token有效期
```bash
# backend/.env
JWT_EXPIRES_IN=7d  # 改为7天
```

#### 启用自动刷新
```typescript
// 在App.vue中已启用
const tokenRefresh = useGlobalTokenRefresh()
```

## 🛠️ 使用方法

### 1. 基础使用
系统会自动管理token，无需手动操作。

### 2. 手动检查Token状态
```typescript
import { useGlobalTokenRefresh } from '@/composables/useTokenRefresh'

const tokenRefresh = useGlobalTokenRefresh()
const status = tokenRefresh.getTokenStatus()

console.log('Token状态:', status)
```

### 3. 添加Token状态显示（可选）
```vue
<template>
  <div class="header">
    <!-- 其他内容 -->
    <TokenStatus />
  </div>
</template>

<script setup>
import TokenStatus from '@/components/TokenStatus.vue'
</script>
```

## 📊 监控和调试

### 1. 控制台日志
系统会在控制台输出token相关日志：
- `🔄 自动刷新token...`
- `✅ Token刷新成功`
- `⚠️ Token已过期`

### 2. Token状态检查
```javascript
// 在浏览器控制台中执行
console.log('Token信息:', localStorage.getItem('token_info'))
```

### 3. 手动测试
```javascript
// 手动触发token检查
window.dispatchEvent(new Event('visibilitychange'))
```

## 🎯 最佳实践

### 1. 开发环境配置
```bash
# 开发时使用较长的过期时间
JWT_EXPIRES_IN=7d
```

### 2. 生产环境配置
```bash
# 生产环境平衡安全性和用户体验
JWT_EXPIRES_IN=1d
```

### 3. 用户提示优化
- 在重要操作前检查token状态
- 提供"保存草稿"功能
- 在token即将过期时主动提醒

## 🔧 故障排除

### 1. 仍然频繁跳转登录
**检查项目**:
- 后端JWT配置是否正确
- 网络连接是否稳定
- 浏览器是否禁用localStorage

**解决方法**:
```bash
# 检查后端日志
npm run dev  # 在backend目录

# 检查前端控制台
# 查看Network标签页的401错误
```

### 2. Token刷新失败
**可能原因**:
- 刷新接口未实现
- 网络问题
- 服务器错误

**解决方法**:
```typescript
// 检查刷新接口
const response = await authApi.refreshToken(token)
```

### 3. 多标签页同步问题
**解决方案**:
```typescript
// 监听storage变化
window.addEventListener('storage', (e) => {
  if (e.key === 'auth_token') {
    // 同步token状态
  }
})
```

## 📈 性能优化

### 1. 减少检查频率
```typescript
// 根据使用场景调整检查间隔
const CHECK_INTERVAL = 30 * 1000 // 30秒
```

### 2. 智能刷新策略
```typescript
// 只在用户活跃时刷新
if (document.visibilityState === 'visible') {
  autoRefreshToken()
}
```

### 3. 缓存优化
```typescript
// 缓存token信息，减少解析次数
const tokenInfo = tokenManager.getTokenInfo()
```

## 🎉 总结

通过实施以上解决方案，应用现在具备：

- ✅ **自动token管理**: 无需用户干预
- ✅ **智能刷新机制**: 在过期前自动刷新
- ✅ **用户友好提示**: 渐进式错误处理
- ✅ **状态同步**: 多标签页状态一致
- ✅ **性能优化**: 最小化资源消耗

用户应该不再遇到频繁跳转登录页面的问题！
