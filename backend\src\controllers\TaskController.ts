import { Response } from 'express';
import { Op } from 'sequelize';
import { ProcessingTask } from '../models/ProcessingTask';
import { AuthRequest } from '../middleware/auth';
import { downloadFile } from '../utils/fileUtils';

export class TaskController {
  // 获取用户任务列表
  async getUserTasks(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { page = 1, limit = 20, status, taskType } = req.query;
      
      const whereConditions: any = { userId: user.id };

      if (status) whereConditions.status = status;
      if (taskType) whereConditions.taskType = taskType;

      const skip = (Number(page) - 1) * Number(limit);

      const [tasks, total] = await Promise.all([
        ProcessingTask.findAll({
          where: whereConditions,
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit),
          include: ['fileUpload']
        }),
        ProcessingTask.count({ where: whereConditions })
      ]);
      
      res.json({
        tasks,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get user tasks error:', error);
      res.status(500).json({ error: '获取任务列表失败' });
    }
  }
  
  // 获取任务详情
  async getTaskById(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { taskId } = req.params;
      
      const task = await ProcessingTask.findOne({
        where: {
          id: taskId,
          userId: user.id
        },
        include: ['fileUpload']
      });
      
      if (!task) {
        res.status(404).json({ error: '任务不存在' });
        return;
      }
      
      res.json({ task });
    } catch (error) {
      console.error('Get task by id error:', error);
      res.status(500).json({ error: '获取任务详情失败' });
    }
  }
  
  // 获取任务状态
  async getTaskStatus(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { taskId } = req.params;
      
      const task = await ProcessingTask.findOne({
        where: {
          id: taskId,
          userId: user.id
        }
      });
      
      if (!task) {
        res.status(404).json({ error: '任务不存在' });
        return;
      }
      
      res.json({
        taskId: task.id,
        status: task.status,
        progress: task.progress,
        errorMessage: task.errorMessage,
        createdAt: task.createdAt,
        startedAt: task.startedAt,
        completedAt: task.completedAt
      });
    } catch (error) {
      console.error('Get task status error:', error);
      res.status(500).json({ error: '获取任务状态失败' });
    }
  }
  
  // 取消任务
  async cancelTask(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { taskId } = req.params;
      
      const task = await ProcessingTask.findOne({
        where: {
          id: taskId,
          userId: user.id
        }
      });
      
      if (!task) {
        res.status(404).json({ error: '任务不存在' });
        return;
      }
      
      if (task.status === 'completed' || task.status === 'failed') {
        res.status(400).json({ error: '任务已完成或失败，无法取消' });
        return;
      }
      
      task.status = 'failed';
      task.errorMessage = '用户取消';
      task.completedAt = new Date();
      await task.save();
      
      res.json({ message: '任务已取消' });
    } catch (error) {
      console.error('Cancel task error:', error);
      res.status(500).json({ error: '取消任务失败' });
    }
  }
  
  // 重试失败的任务
  async retryTask(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { taskId } = req.params;
      
      const task = await ProcessingTask.findOne({
        where: {
          id: taskId,
          userId: user.id
        }
      });
      
      if (!task) {
        res.status(404).json({ error: '任务不存在' });
        return;
      }
      
      if (task.status !== 'failed') {
        res.status(400).json({ error: '只能重试失败的任务' });
        return;
      }
      
      // 重置任务状态
      task.status = 'pending';
      task.progress = 0;
      task.errorMessage = undefined;
      task.startedAt = undefined;
      task.completedAt = undefined;
      await task.save();
      
      // 这里应该重新加入任务队列
      // TODO: 实现任务重试逻辑
      
      res.json({ message: '任务已重新提交' });
    } catch (error) {
      console.error('Retry task error:', error);
      res.status(500).json({ error: '重试任务失败' });
    }
  }
  
  // 获取任务结果
  async getTaskResult(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { taskId } = req.params;
      
      const task = await ProcessingTask.findOne({
        where: {
          id: taskId,
          userId: user.id
        }
      });
      
      if (!task) {
        res.status(404).json({ error: '任务不存在' });
        return;
      }
      
      if (task.status !== 'completed') {
        res.status(400).json({ error: '任务尚未完成' });
        return;
      }
      
      res.json({
        taskId: task.id,
        taskType: task.taskType,
        status: task.status,
        resultData: task.resultData,
        completedAt: task.completedAt
      });
    } catch (error) {
      console.error('Get task result error:', error);
      res.status(500).json({ error: '获取任务结果失败' });
    }
  }
  
  // 下载任务结果文件
  async downloadTaskResult(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { taskId } = req.params;
      
      const task = await ProcessingTask.findOne({
        where: {
          id: taskId,
          userId: user.id
        }
      });
      
      if (!task) {
        res.status(404).json({ error: '任务不存在' });
        return;
      }
      
      if (task.status !== 'completed') {
        res.status(400).json({ error: '任务尚未完成' });
        return;
      }
      
      if (!task.resultData?.outputPath) {
        res.status(404).json({ error: '结果文件不存在' });
        return;
      }
      
      const filePath = task.resultData.outputPath;
      const filename = task.resultData.outputFilename || 'result';
      
      await downloadFile(res, filePath, filename);
    } catch (error) {
      console.error('Download task result error:', error);
      res.status(500).json({ error: '下载结果文件失败' });
    }
  }
}
