import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';

export interface AuthRequest extends Request {
  user?: any;
}

export const authenticateToken = async (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    
    if (!token) {
      res.status(401).json({ error: '访问令牌缺失' });
      return;
    }
    
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }
    
    const decoded = jwt.verify(token, jwtSecret) as { userId: string };
    const user = await User.findByPk(decoded.userId);
    
    if (!user || !user.isActive) {
      res.status(401).json({ error: '用户不存在或已被禁用' });
      return;
    }
    
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({ error: '无效的访问令牌' });
    } else if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({ error: '访问令牌已过期' });
    } else {
      console.error('Authentication error:', error);
      res.status(500).json({ error: '认证服务错误' });
    }
  }
};

export const requireAdmin = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({ error: '未认证' });
    return;
  }
  
  if (req.user.userType !== 'admin') {
    res.status(403).json({ error: '需要管理员权限' });
    return;
  }
  
  next();
};

export const requirePremium = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    res.status(401).json({ error: '未认证' });
    return;
  }
  
  if (req.user.userType === 'free') {
    res.status(403).json({ error: '需要高级会员权限' });
    return;
  }
  
  next();
};

export const checkFeatureUsage = (feature: 'pdfConvert' | 'questionParse' | 'examGenerate') => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      res.status(401).json({ error: '未认证' });
      return;
    }
    
    if (!req.user.canUseFeature(feature)) {
      res.status(429).json({ 
        error: '今日使用次数已达上限',
        feature,
        userType: req.user.userType,
        dailyUsage: req.user.dailyUsage
      });
      return;
    }
    
    next();
  };
};
