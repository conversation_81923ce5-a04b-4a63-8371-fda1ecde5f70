<template>
  <Layout>
    <div class="question-parse">
      <div class="page-header">
        <h2>试题解析</h2>
        <p>智能解析试卷文档，自动提取题目信息并入库</p>
      </div>

      <!-- 使用限制提示 -->
      <div class="usage-alert" v-if="!authStore.canUseFeature('questionParse')">
        <el-alert
          title="使用次数已达上限"
          type="warning"
          :description="`免费用户每日可解析${usage.limit}次，您今日已使用${usage.used}次。升级到高级会员可享受无限次解析。`"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :on-progress="onUploadProgress"
          :disabled="uploading || !authStore.canUseFeature('questionParse')"
          accept=".pdf,.doc,.docx"
          :show-file-list="false"
        >
          <div class="upload-content">
            <el-icon class="upload-icon" size="48">
              <UploadFilled v-if="!uploading" />
              <Loading v-else />
            </el-icon>
            <div class="upload-text">
              <p v-if="!uploading">点击或拖拽试卷文件到此处上传</p>
              <p v-else>正在上传文件...</p>
            </div>
            <div class="upload-hint">
              支持PDF、Word格式，文件大小不超过50MB
            </div>
          </div>
        </el-upload>

        <!-- 上传进度 -->
        <div v-if="uploading" class="progress-section">
          <el-progress
            :percentage="uploadProgress"
            :status="uploadProgress === 100 ? 'success' : undefined"
          />
          <p class="progress-text">{{ uploadProgressText }}</p>
        </div>
      </div>

      <!-- 解析说明 -->
      <div class="info-section" v-if="!uploading">
        <div class="card">
          <div class="card-header">
            <h3>解析说明</h3>
          </div>
          <div class="info-content">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-icon">
                  <el-icon size="24" color="#409eff"><Document /></el-icon>
                </div>
                <div class="info-text">
                  <h4>支持格式</h4>
                  <p>PDF、Word文档，推荐使用PDF格式以获得更好的解析效果</p>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-icon">
                  <el-icon size="24" color="#67c23a"><EditPen /></el-icon>
                </div>
                <div class="info-text">
                  <h4>智能识别</h4>
                  <p>自动识别题号、题干、选项、答案和解析，支持数学公式和图表</p>
                </div>
              </div>
              
              <div class="info-item">
                <div class="info-icon">
                  <el-icon size="24" color="#e6a23c"><Collection /></el-icon>
                </div>
                <div class="info-text">
                  <h4>自动入库</h4>
                  <p>解析完成后自动保存到您的题库，支持分类和标签管理</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 解析历史 -->
      <div class="history-section">
        <div class="card">
          <div class="card-header">
            <h3>解析历史</h3>
            <el-button @click="loadHistory" :loading="historyLoading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <el-table :data="historyList" v-loading="historyLoading">
            <el-table-column prop="originalFilename" label="文件名" min-width="200">
              <template #default="{ row }">
                <div class="filename">
                  <el-icon><Document /></el-icon>
                  <span>{{ row.originalFilename }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="fileSize" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="解析结果" width="150">
              <template #default="{ row }">
                <span v-if="row.task && row.task.resultData">
                  {{ row.task.resultData.questionsCount || 0 }} 道题目
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="createdAt" label="上传时间" width="160">
              <template #default="{ row }">
                {{ formatTime(row.createdAt) }}
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    v-if="row.status === 'completed' && row.task?.resultData?.questionsCount"
                    type="primary"
                    size="small"
                    @click="viewQuestions(row.id)"
                  >
                    <el-icon><View /></el-icon>
                    查看题目
                  </el-button>
                  
                  <el-button
                    v-if="row.status === 'processing'"
                    type="info"
                    size="small"
                    @click="checkTaskStatus(row.task?.id)"
                  >
                    <el-icon><Loading /></el-icon>
                    查看进度
                  </el-button>
                  
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteFile(row.id)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="pagination.total > 0">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadHistory"
              @current-change="loadHistory"
            />
          </div>
        </div>
      </div>

      <!-- 解析结果预览对话框 -->
      <el-dialog
        v-model="previewVisible"
        title="解析结果预览"
        width="80%"
        :before-close="closePreview"
      >
        <div class="preview-content" v-loading="previewLoading">
          <div v-if="previewQuestions.length > 0" class="questions-list">
            <div 
              v-for="(question, index) in previewQuestions" 
              :key="question.id"
              class="question-preview"
            >
              <div class="question-header">
                <span class="question-number">{{ question.questionNumber || (index + 1) }}</span>
                <el-tag :type="getQuestionTypeColor(question.questionType)" size="small">
                  {{ getQuestionTypeText(question.questionType) }}
                </el-tag>
              </div>
              
              <div class="question-stem">{{ question.stem }}</div>
              
              <div v-if="question.options && question.options.length > 0" class="question-options">
                <div 
                  v-for="option in question.options" 
                  :key="option.key"
                  class="option-item"
                >
                  <span class="option-key">{{ option.key }}.</span>
                  <span class="option-content">{{ option.content }}</span>
                </div>
              </div>
              
              <div v-if="question.correctAnswer" class="question-answer">
                <strong>答案：</strong>{{ question.correctAnswer }}
              </div>
            </div>
          </div>
          
          <el-empty v-else description="暂无解析结果" />
        </div>
        
        <template #footer>
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="goToQuestionBank">
            前往题库管理
          </el-button>
        </template>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  UploadFilled,
  Loading,
  Document,
  EditPen,
  Collection,
  Refresh,
  View,
  Delete
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import { http } from '@/utils/http'

const router = useRouter()
const authStore = useAuthStore()

const uploading = ref(false)
const uploadProgress = ref(0)
const uploadProgressText = ref('')
const historyLoading = ref(false)
const historyList = ref([])
const previewVisible = ref(false)
const previewLoading = ref(false)
const previewQuestions = ref([])

const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

const uploadAction = import.meta.env.VITE_API_BASE_URL + '/file/upload/question-parse'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

const usage = computed(() => authStore.getFeatureUsage('questionParse'))

const beforeUpload = (file: File) => {
  const allowedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ]
  const isAllowed = allowedTypes.includes(file.type)
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isAllowed) {
    ElMessage.error('只能上传PDF或Word格式的文件！')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB！')
    return false
  }

  uploading.value = true
  uploadProgress.value = 0
  uploadProgressText.value = '准备上传...'
  
  return true
}

const onUploadProgress = (event: any) => {
  uploadProgress.value = Math.round((event.loaded / event.total) * 100)
  uploadProgressText.value = `上传进度: ${uploadProgress.value}%`
}

const onUploadSuccess = (response: any) => {
  uploading.value = false
  ElMessage.success('文件上传成功，正在解析中...')
  loadHistory()
  
  // 重置用户使用次数
  authStore.getCurrentUser()
}

const onUploadError = (error: any) => {
  uploading.value = false
  console.error('Upload error:', error)
  ElMessage.error('文件上传失败，请重试')
}

const loadHistory = async () => {
  historyLoading.value = true
  try {
    const response = await http.get('/file/uploads', {
      params: {
        page: pagination.value.page,
        limit: pagination.value.limit,
        uploadType: 'question_parse'
      }
    })
    
    historyList.value = response.files
    pagination.value.total = response.pagination.total
  } catch (error) {
    console.error('Load history error:', error)
  } finally {
    historyLoading.value = false
  }
}

const checkTaskStatus = async (taskId: string) => {
  if (!taskId) return
  
  try {
    const response = await http.get(`/task/${taskId}/status`)
    ElMessage.info(`任务状态: ${getStatusText(response.status)}, 进度: ${response.progress}%`)
  } catch (error) {
    console.error('Check task status error:', error)
  }
}

const viewQuestions = async (fileId: string) => {
  previewVisible.value = true
  previewLoading.value = true
  
  try {
    const response = await http.get('/question', {
      params: {
        sourceFileId: fileId,
        limit: 20
      }
    })
    previewQuestions.value = response.questions || []
  } catch (error) {
    console.error('Load questions error:', error)
    ElMessage.error('加载题目失败')
  } finally {
    previewLoading.value = false
  }
}

const deleteFile = async (fileId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？删除后相关的题目也会被删除。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await http.delete(`/file/uploads/${fileId}`)
    ElMessage.success('文件删除成功')
    loadHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete error:', error)
    }
  }
}

const closePreview = () => {
  previewVisible.value = false
  previewQuestions.value = []
}

const goToQuestionBank = () => {
  closePreview()
  router.push('/question-bank')
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / 1024 / 1024).toFixed(1)} MB`
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'processing': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'processing': return '解析中'
    case 'failed': return '失败'
    case 'uploaded': return '已上传'
    default: return '未知'
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'single_choice': return 'primary'
    case 'multiple_choice': return 'success'
    case 'true_false': return 'warning'
    case 'fill_blank': return 'info'
    default: return 'default'
  }
}

const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'single_choice': return '单选题'
    case 'multiple_choice': return '多选题'
    case 'true_false': return '判断题'
    case 'fill_blank': return '填空题'
    case 'short_answer': return '简答题'
    case 'essay': return '论述题'
    case 'calculation': return '计算题'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.question-parse {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.usage-alert {
  margin-bottom: 20px;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-dragger {
  width: 100%;
}

.upload-content {
  padding: 40px;
  text-align: center;
}

.upload-icon {
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: #909399;
}

.progress-section {
  margin-top: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.info-section {
  margin-bottom: 30px;
}

.info-content {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-text h4 {
  font-size: 16px;
  margin: 0 0 4px 0;
  color: #333;
}

.info-text p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.history-section .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8eaec;
}

.card-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.filename {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.preview-content {
  max-height: 60vh;
  overflow-y: auto;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-preview {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 16px;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.question-number {
  font-weight: 600;
  color: #409eff;
}

.question-stem {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 12px;
}

.question-options {
  margin-bottom: 12px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.option-key {
  font-weight: 600;
  margin-right: 8px;
  min-width: 20px;
}

.option-content {
  flex: 1;
  line-height: 1.5;
}

.question-answer {
  font-size: 14px;
  color: #67c23a;
}

@media (max-width: 768px) {
  .upload-content {
    padding: 20px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
