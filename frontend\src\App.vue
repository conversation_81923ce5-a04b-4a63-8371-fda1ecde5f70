<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useGlobalTokenRefresh } from '@/composables/useTokenRefresh'

const authStore = useAuthStore()
const tokenRefresh = useGlobalTokenRefresh()

onMounted(() => {
  // 应用启动时检查登录状态
  authStore.checkAuth()
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

#app {
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 样式覆盖 */
.el-button {
  font-weight: 500;
}

.el-button--primary {
  background: linear-gradient(135deg, #409eff, #337ecc);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #337ecc, #2b6cb0);
}
</style>
