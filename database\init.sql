-- d2x 数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS datedu_hw CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE datedu_hw;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    passwordHash VARCHAR(255) NOT NULL,
    fullName VARCHAR(100),
    avatarUrl VARCHAR(500),
    userType ENUM('free', 'premium', 'admin') NOT NULL DEFAULT 'free',
    isActive BOOLEAN NOT NULL DEFAULT TRUE,
    isEmailVerified BOOLEAN NOT NULL DEFAULT FALSE,
    emailVerificationToken VARCHAR(255) NULL,
    emailVerificationExpires DATETIME NULL,
    subscriptionExpiresAt DATETIME NULL,
    dailyUsage JSON NOT NULL DEFAULT '{"pdfConvert": 0, "questionParse": 0, "examGenerate": 0}',
    lastUsageReset DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_userType (userType),
    INDEX idx_isActive (isActive)
);

-- 创建文件上传表
CREATE TABLE IF NOT EXISTS file_uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userId INT NOT NULL,
    originalFilename VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    filepath VARCHAR(500) NOT NULL,
    mimetype VARCHAR(100) NOT NULL,
    fileSize BIGINT NOT NULL,
    uploadType ENUM('pdf_convert', 'question_parse') NOT NULL,
    status ENUM('uploaded', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'uploaded',
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_userId (userId),
    INDEX idx_uploadType (uploadType),
    INDEX idx_status (status),
    INDEX idx_createdAt (createdAt)
);

-- 创建题目表
CREATE TABLE IF NOT EXISTS questions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userId INT NOT NULL,
    questionNumber VARCHAR(20),
    questionType ENUM('single_choice', 'multiple_choice', 'true_false', 'fill_blank', 'short_answer', 'essay', 'calculation') NOT NULL,
    subject VARCHAR(100),
    difficultyLevel INT NOT NULL DEFAULT 3 CHECK (difficultyLevel >= 1 AND difficultyLevel <= 5),
    stem TEXT NOT NULL,
    options JSON,
    correctAnswer TEXT,
    explanation TEXT,
    tags JSON NOT NULL DEFAULT '[]',
    sourceFileId INT,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sourceFileId) REFERENCES file_uploads(id) ON DELETE SET NULL,
    INDEX idx_userId (userId),
    INDEX idx_questionType (questionType),
    INDEX idx_subject (subject),
    INDEX idx_difficultyLevel (difficultyLevel),
    INDEX idx_sourceFileId (sourceFileId),
    INDEX idx_createdAt (createdAt)
);

-- 创建处理任务表
CREATE TABLE IF NOT EXISTS processing_tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userId INT NOT NULL,
    fileUploadId INT,
    taskType ENUM('pdf_to_word', 'question_parsing', 'exam_generation') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    progress INT NOT NULL DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    errorMessage TEXT,
    resultData JSON,
    startedAt DATETIME,
    completedAt DATETIME,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (fileUploadId) REFERENCES file_uploads(id) ON DELETE SET NULL,
    INDEX idx_userId (userId),
    INDEX idx_fileUploadId (fileUploadId),
    INDEX idx_taskType (taskType),
    INDEX idx_status (status),
    INDEX idx_createdAt (createdAt)
);

-- 创建试卷表
CREATE TABLE IF NOT EXISTS exams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userId INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    subject VARCHAR(100),
    examType ENUM('practice', 'test', 'homework', 'quiz') NOT NULL DEFAULT 'practice',
    totalScore INT NOT NULL DEFAULT 0 CHECK (totalScore >= 0),
    timeLimit INT CHECK (timeLimit >= 1 AND timeLimit <= 600),
    instructions TEXT,
    questions JSON NOT NULL DEFAULT '[]',
    isPublished BOOLEAN NOT NULL DEFAULT FALSE,
    createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (userId) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_userId (userId),
    INDEX idx_examType (examType),
    INDEX idx_subject (subject),
    INDEX idx_isPublished (isPublished),
    INDEX idx_createdAt (createdAt)
);

-- 插入默认管理员账号（密码：admin123）
INSERT IGNORE INTO users (
    username,
    email,
    passwordHash,
    fullName,
    userType,
    isActive,
    isEmailVerified,
    dailyUsage,
    lastUsageReset
) VALUES (
    'admin',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- admin123
    '系统管理员',
    'admin',
    TRUE,
    TRUE, -- 管理员账号默认已验证邮箱
    '{"pdfConvert": 0, "questionParse": 0, "examGenerate": 0}',
    NOW()
);

-- 插入示例题目
INSERT IGNORE INTO questions (
    userId,
    questionNumber,
    questionType,
    subject,
    difficultyLevel,
    stem,
    options,
    correctAnswer,
    explanation,
    tags
) VALUES 
(
    1, -- 管理员用户ID
    '1',
    'single_choice',
    '数学',
    3,
    '下列哪个数是质数？',
    '[{"key": "A", "content": "4", "isCorrect": false}, {"key": "B", "content": "6", "isCorrect": false}, {"key": "C", "content": "7", "isCorrect": true}, {"key": "D", "content": "8", "isCorrect": false}]',
    'C',
    '质数是只能被1和自身整除的大于1的自然数。7只能被1和7整除，所以是质数。',
    '["基础", "数论"]'
),
(
    1,
    '2',
    'multiple_choice',
    '数学',
    2,
    '下列哪些数是偶数？',
    '[{"key": "A", "content": "2", "isCorrect": true}, {"key": "B", "content": "3", "isCorrect": false}, {"key": "C", "content": "4", "isCorrect": true}, {"key": "D", "content": "5", "isCorrect": false}]',
    'A,C',
    '偶数是能被2整除的整数。2和4都能被2整除，所以都是偶数。',
    '["基础", "数论"]'
),
(
    1,
    '3',
    'true_false',
    '数学',
    1,
    '0是自然数。',
    '[{"key": "A", "content": "正确", "isCorrect": true}, {"key": "B", "content": "错误", "isCorrect": false}]',
    'A',
    '根据现代数学定义，0是自然数。',
    '["基础", "概念"]'
);
