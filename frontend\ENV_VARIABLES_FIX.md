# 🔧 前端环境变量修复说明

## 🚨 问题描述
```
找不到名称"process"。是否需要安装 Node.js 的类型定义?
```

## 🔍 问题原因
在Vite构建的Vue项目中，不能直接使用`process.env`来访问环境变量，需要使用`import.meta.env`。

## ✅ 修复内容

### 1. 修复http.ts文件
```typescript
// 修复前（错误）
baseURL: process.env.VITE_API_BASE_URL,

// 修复后（正确）
baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
```

### 2. 创建环境变量文件
- ✅ `frontend/.env` - 基础环境变量
- ✅ `frontend/.env.development` - 开发环境配置（已存在）
- ✅ `frontend/.env.example` - 示例配置（已存在）

### 3. 添加类型定义
- ✅ `frontend/src/types/env.d.ts` - 环境变量类型定义
- ✅ `frontend/src/utils/env.ts` - 环境变量工具函数

## 📋 环境变量配置

### 当前配置
```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:3000/api

# 应用信息
VITE_APP_TITLE=d2x智能试题解析平台
VITE_APP_VERSION=1.0.0
VITE_NODE_ENV=development
```

### 使用方式

#### 直接访问
```typescript
// 获取API基础URL
const apiUrl = import.meta.env.VITE_API_BASE_URL

// 获取应用标题
const appTitle = import.meta.env.VITE_APP_TITLE
```

#### 使用工具函数（推荐）
```typescript
import { getApiBaseUrl, getAppTitle, isDevelopment } from '@/utils/env'

// 获取API基础URL
const apiUrl = getApiBaseUrl()

// 获取应用标题
const appTitle = getAppTitle()

// 检查是否为开发环境
if (isDevelopment()) {
  console.log('开发环境')
}
```

## 🔧 Vite环境变量规则

### 1. 命名规则
- 必须以`VITE_`开头
- 例如：`VITE_API_BASE_URL`、`VITE_APP_TITLE`

### 2. 访问方式
- 使用`import.meta.env.VARIABLE_NAME`
- 不能使用`process.env.VARIABLE_NAME`

### 3. 类型安全
- 在`src/types/env.d.ts`中定义类型
- 获得完整的TypeScript支持

## 🚀 验证修复

### 1. 检查编译错误
```bash
cd frontend
npm run type-check
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 检查控制台
打开浏览器控制台，应该能看到环境变量信息（开发环境）。

## 📚 相关文档

- [Vite环境变量文档](https://vitejs.dev/guide/env-and-mode.html)
- [Vue 3 + TypeScript最佳实践](https://vuejs.org/guide/typescript/overview.html)

## 🎯 最佳实践

### 1. 环境变量管理
- 敏感信息不要放在前端环境变量中
- 使用不同的环境文件管理不同环境的配置
- 为所有环境变量提供默认值

### 2. 类型安全
- 始终为环境变量定义TypeScript类型
- 使用工具函数封装环境变量访问
- 在运行时验证关键环境变量

### 3. 调试支持
- 在开发环境中打印环境变量信息
- 提供环境变量检查工具
- 记录环境变量使用情况

---

**修复完成**: 前端环境变量问题已解决，项目可以正常编译和运行。
