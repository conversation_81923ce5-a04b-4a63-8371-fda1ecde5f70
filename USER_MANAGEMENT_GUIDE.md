# 👥 管理员用户管理功能指南

## 📋 功能概述

管理员可以通过用户管理界面查看、管理和操作系统中的所有用户账户。

## 🔐 访问权限

### 管理员权限要求
- 用户类型必须为 `admin`
- 已登录并通过身份验证
- 具有管理员权限的用户才能访问用户管理功能

### 访问方式
1. **侧边栏菜单**: 系统管理 → 用户管理
2. **用户下拉菜单**: 点击头像 → 用户管理
3. **直接访问**: `/admin/users`

## 🎯 主要功能

### 1. 用户列表查看
- **用户信息显示**:
  - 用户ID
  - 用户名和头像
  - 邮箱地址
  - 真实姓名
  - 用户类型（免费用户/高级会员/管理员）
  - 账户状态（活跃/禁用）
  - 邮箱验证状态
  - 注册时间

### 2. 筛选和搜索
- **用户类型筛选**:
  - 全部用户
  - 免费用户
  - 高级会员
  - 管理员

- **账户状态筛选**:
  - 全部状态
  - 活跃用户
  - 禁用用户

- **关键词搜索**:
  - 支持用户名搜索
  - 支持邮箱搜索
  - 支持真实姓名搜索

### 3. 用户操作
- **查看详情**: 查看用户的完整信息
- **启用/禁用**: 切换用户账户状态
- **修改类型**: 更改用户类型（免费/高级/管理员）
- **重置密码**: 重置用户密码（开发中）
- **查看使用记录**: 查看用户的使用历史（开发中）

### 4. 分页浏览
- 支持分页显示用户列表
- 可调整每页显示数量（10/20/50/100）
- 显示总用户数和页数信息

## 🖥️ 界面说明

### 页面布局
```
┌─────────────────────────────────────────┐
│ 用户管理                                │
│ 管理系统中的所有用户账户                │
├─────────────────────────────────────────┤
│ [用户类型▼] [状态▼] [搜索框] [刷新]     │
├─────────────────────────────────────────┤
│ 用户列表                    总计: X 个用户│
│ ┌─────┬────────┬──────┬────┬────┬────┐ │
│ │ ID  │ 用户名 │ 邮箱 │类型│状态│操作│ │
│ ├─────┼────────┼──────┼────┼────┼────┤ │
│ │ ... │ ...    │ ...  │... │... │... │ │
│ └─────┴────────┴──────┴────┴────┴────┘ │
│ [分页控件]                              │
└─────────────────────────────────────────┘
```

### 用户类型标识
- 🔴 **管理员**: 红色标签
- 🟡 **高级会员**: 黄色标签  
- 🔵 **免费用户**: 蓝色标签

### 状态标识
- ✅ **活跃**: 绿色标签
- ❌ **禁用**: 红色标签
- ✅ **邮箱已验证**: 绿色勾号
- ❌ **邮箱未验证**: 红色叉号

## 🔧 操作指南

### 查看用户详情
1. 点击用户行的"详情"按钮
2. 弹出对话框显示用户完整信息
3. 包括注册时间、最后更新时间、会员到期时间等

### 启用/禁用用户
1. 点击用户行的"启用"或"禁用"按钮
2. 确认操作提示
3. 系统更新用户状态
4. 禁用的用户无法登录系统

### 修改用户类型
1. 点击用户行的"更多" → "修改类型"
2. 选择新的用户类型
3. 确认修改
4. 系统自动更新用户权限

### 搜索用户
1. 在搜索框输入关键词
2. 按回车键或点击搜索按钮
3. 系统显示匹配的用户列表
4. 支持模糊搜索

## 📊 API接口

### 获取用户列表
```http
GET /api/user/all
Authorization: Bearer <admin_token>

Query Parameters:
- page: 页码 (默认: 1)
- limit: 每页数量 (默认: 20)
- userType: 用户类型筛选 (可选)
- isActive: 状态筛选 (可选)
- keyword: 搜索关键词 (可选)
```

### 更新用户状态
```http
PUT /api/user/:userId/status
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "isActive": true/false
}
```

### 更新用户类型
```http
PUT /api/user/:userId/type
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "userType": "free|premium|admin"
}
```

## 🛡️ 安全考虑

### 权限控制
- 只有管理员可以访问用户管理功能
- 前端路由守卫检查管理员权限
- 后端API使用`requireAdmin`中间件验证

### 操作日志
- 所有用户管理操作都会记录日志
- 包括操作者、操作时间、操作内容
- 便于审计和问题追踪

### 数据保护
- 用户密码哈希不会在API中返回
- 敏感信息只对管理员可见
- 操作需要二次确认

## 🎯 使用场景

### 日常管理
- 查看新注册用户
- 处理用户账户问题
- 管理用户权限

### 用户支持
- 协助用户解决登录问题
- 处理账户升级请求
- 查看用户使用情况

### 系统维护
- 清理无效账户
- 管理测试账户
- 监控用户活跃度

## 🔮 未来功能

### 计划中的功能
- **批量操作**: 批量启用/禁用用户
- **导出功能**: 导出用户列表到Excel
- **高级筛选**: 按注册时间、最后登录时间筛选
- **用户统计**: 用户增长趋势图表
- **操作日志**: 查看用户操作历史记录

### 增强功能
- **重置密码**: 为用户重置密码并发送邮件
- **使用记录**: 查看用户的详细使用记录
- **消息通知**: 向用户发送系统消息
- **账户分析**: 用户行为分析报告

## 📝 注意事项

1. **谨慎操作**: 禁用用户会立即阻止其登录
2. **权限变更**: 修改用户类型会影响其功能权限
3. **数据备份**: 重要操作前建议备份用户数据
4. **测试环境**: 新功能先在测试环境验证

## 🎉 总结

用户管理功能为管理员提供了完整的用户账户管理能力：

- ✅ **全面查看**: 查看所有用户信息和状态
- ✅ **灵活筛选**: 多维度筛选和搜索用户
- ✅ **便捷操作**: 快速启用/禁用和修改用户类型
- ✅ **安全可靠**: 完善的权限控制和操作确认
- ✅ **用户友好**: 直观的界面和清晰的操作流程

通过这个功能，管理员可以高效地管理系统中的所有用户账户！
