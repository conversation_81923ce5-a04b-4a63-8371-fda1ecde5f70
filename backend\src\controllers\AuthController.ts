import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Op } from 'sequelize';
import { User } from '../models/User';
import { AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import { emailService } from '../services/EmailService';

export class AuthController {
  // 用户注册
  async register(req: Request, res: Response): Promise<void> {
    try {
      const { username, email, password, fullName } = req.body;
      
      // 验证必填字段
      if (!username || !email || !password) {
        res.status(400).json({ error: '用户名、邮箱和密码为必填项' });
        return;
      }
      
      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        res.status(400).json({ error: '邮箱格式不正确' });
        return;
      }
      
      // 验证密码强度
      if (password.length < 6) {
        res.status(400).json({ error: '密码长度至少6位' });
        return;
      }
      
      // 检查用户是否已存在
      const existingUser = await User.findOne({
        where: {
          [Op.or]: [{ email }, { username }]
        }
      });
      
      if (existingUser) {
        res.status(409).json({ 
          error: existingUser.email === email ? '邮箱已被注册' : '用户名已被使用' 
        });
        return;
      }
      
      // 加密密码
      const saltRounds = 10;
      const passwordHash = await bcrypt.hash(password, saltRounds);
      
      // 创建用户
      const user = await User.create({
        username,
        email: email.toLowerCase(),
        passwordHash,
        fullName,
        userType: 'free', // 默认为免费用户
        isActive: true,   // 默认激活
        isEmailVerified: false,
        dailyUsage: {
          pdfConvert: 0,
          questionParse: 0,
          examGenerate: 0
        },
        lastUsageReset: new Date()
      });

      // 生成邮箱验证令牌
      const verificationToken = user.generateEmailVerificationToken();
      await user.save();

      // 发送验证邮件
      try {
        await emailService.sendEmailVerification(user, verificationToken);
      } catch (emailError) {
        console.error('发送验证邮件失败:', emailError);
        // 邮件发送失败不应该阻止注册流程
      }

      res.status(201).json({
        message: '注册成功，请检查您的邮箱并点击验证链接',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          userType: user.userType,
          avatarUrl: user.avatarUrl,
          isEmailVerified: user.isEmailVerified
        },
        requiresEmailVerification: true
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ error: '注册失败，请稍后重试' });
    }
  }
  
  // 用户登录
  async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        res.status(400).json({ error: '用户名/邮箱和密码为必填项' });
        return;
      }

      // 判断输入的是邮箱还是用户名
      const isEmail = this.isValidEmail(email);

      // 查找用户 - 支持邮箱或用户名登录
      const user = await User.findOne({
        where: {
          [isEmail ? 'email' : 'username']: isEmail ? email.toLowerCase() : email,
          isActive: true
        }
      });

      if (!user) {
        res.status(401).json({ error: '用户名/邮箱或密码错误' });
        return;
      }
      
      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      if (!isPasswordValid) {
        res.status(401).json({ error: '用户名/邮箱或密码错误' });
        return;
      }

      // 检查邮箱是否已验证
      if (!user.isEmailVerified) {
        res.status(403).json({
          error: '请先验证您的邮箱地址',
          code: 'EMAIL_NOT_VERIFIED',
          email: user.email
        });
        return;
      }

      // 生成JWT令牌
      const token = this.generateToken(user.id.toString());
      
      res.json({
        message: '登录成功',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          userType: user.userType,
          avatarUrl: user.avatarUrl,
          isEmailVerified: user.isEmailVerified,
          subscriptionExpiresAt: user.subscriptionExpiresAt
        },
        token
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ error: '登录失败，请稍后重试' });
    }
  }
  
  // 获取当前用户信息
  async getCurrentUser(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      
      res.json({
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          userType: user.userType,
          avatarUrl: user.avatarUrl,
          isEmailVerified: user.isEmailVerified,
          subscriptionExpiresAt: user.subscriptionExpiresAt,
          dailyUsage: user.dailyUsage,
          lastUsageReset: user.lastUsageReset
        }
      });
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({ error: '获取用户信息失败' });
    }
  }
  
  // 刷新令牌
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.body;
      
      if (!token) {
        res.status(400).json({ error: '令牌为必填项' });
        return;
      }
      
      const jwtSecret = process.env.JWT_SECRET;
      if (!jwtSecret) {
        throw new Error('JWT_SECRET not configured');
      }
      
      const decoded = jwt.verify(token, jwtSecret) as { userId: string };
      const user = await User.findByPk(decoded.userId);
      
      if (!user || !user.isActive) {
        res.status(401).json({ error: '用户不存在或已被禁用' });
        return;
      }
      
      const newToken = this.generateToken(user.id.toString());
      
      res.json({
        message: '令牌刷新成功',
        token: newToken
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(401).json({ error: '令牌刷新失败' });
    }
  }
  
  // 用户登出
  async logout(req: AuthRequest, res: Response): Promise<void> {
    try {
      // 这里可以实现令牌黑名单机制
      res.json({ message: '登出成功' });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({ error: '登出失败' });
    }
  }
  
  // 修改密码
  async changePassword(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { currentPassword, newPassword } = req.body;
      const user = req.user!;
      
      if (!currentPassword || !newPassword) {
        res.status(400).json({ error: '当前密码和新密码为必填项' });
        return;
      }
      
      if (newPassword.length < 6) {
        res.status(400).json({ error: '新密码长度至少6位' });
        return;
      }
      
      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
      if (!isCurrentPasswordValid) {
        res.status(401).json({ error: '当前密码错误' });
        return;
      }
      
      // 加密新密码
      const saltRounds = 10;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
      
      // 更新密码
      user.passwordHash = newPasswordHash;
      await user.save();
      
      res.json({ message: '密码修改成功' });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({ error: '密码修改失败' });
    }
  }
  
  // 忘记密码
  async forgotPassword(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;
      
      if (!email) {
        res.status(400).json({ error: '邮箱为必填项' });
        return;
      }
      
      const user = await User.findOne({
        where: {
          email: email.toLowerCase(),
          isActive: true
        }
      });
      
      if (!user) {
        // 为了安全，不透露用户是否存在
        res.json({ message: '如果邮箱存在，重置链接已发送' });
        return;
      }
      
      // TODO: 实现邮件发送功能
      // 生成重置令牌并发送邮件
      
      res.json({ message: '如果邮箱存在，重置链接已发送' });
    } catch (error) {
      console.error('Forgot password error:', error);
      res.status(500).json({ error: '请求处理失败' });
    }
  }
  
  // 重置密码
  async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, newPassword } = req.body;
      
      if (!token || !newPassword) {
        res.status(400).json({ error: '令牌和新密码为必填项' });
        return;
      }
      
      if (newPassword.length < 6) {
        res.status(400).json({ error: '新密码长度至少6位' });
        return;
      }
      
      // TODO: 验证重置令牌
      // 这里需要实现令牌验证逻辑
      
      res.json({ message: '密码重置成功' });
    } catch (error) {
      console.error('Reset password error:', error);
      res.status(500).json({ error: '密码重置失败' });
    }
  }
  
  // 验证邮箱
  async verifyEmail(req: Request, res: Response): Promise<void> {
    try {
      const { token, email } = req.query;

      if (!token || !email) {
        res.status(400).json({ error: '缺少验证参数' });
        return;
      }

      // 查找用户
      const user = await User.findOne({
        where: {
          email: email as string,
          emailVerificationToken: token as string
        }
      });

      if (!user) {
        res.status(400).json({ error: '无效的验证链接' });
        return;
      }

      // 验证令牌
      if (!user.verifyEmailToken(token as string)) {
        res.status(400).json({ error: '验证链接已过期，请重新发送验证邮件' });
        return;
      }

      // 标记邮箱已验证
      await user.markEmailAsVerified();

      // 发送欢迎邮件
      try {
        await emailService.sendWelcomeEmail(user);
      } catch (emailError) {
        console.error('发送欢迎邮件失败:', emailError);
      }

      res.json({
        message: '邮箱验证成功！欢迎加入 d2x',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isEmailVerified: user.isEmailVerified
        }
      });
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json({ error: '邮箱验证失败' });
    }
  }

  // 重新发送验证邮件
  async resendVerificationEmail(req: Request, res: Response): Promise<void> {
    try {
      const { email } = req.body;

      if (!email) {
        res.status(400).json({ error: '请提供邮箱地址' });
        return;
      }

      // 查找用户
      const user = await User.findOne({
        where: {
          email: email.toLowerCase(),
          isEmailVerified: false
        }
      });

      if (!user) {
        res.status(404).json({ error: '用户不存在或邮箱已验证' });
        return;
      }

      // 生成新的验证令牌
      const verificationToken = user.generateEmailVerificationToken();
      await user.save();

      // 发送验证邮件
      await emailService.sendEmailVerification(user, verificationToken);

      res.json({
        message: '验证邮件已重新发送，请检查您的邮箱'
      });
    } catch (error) {
      console.error('Resend verification email error:', error);
      res.status(500).json({ error: '发送验证邮件失败' });
    }
  }

  // 生成JWT令牌
  private generateToken(userId: string): string {
    const jwtSecret = process.env.JWT_SECRET;
    const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';

    if (!jwtSecret) {
      throw new Error('JWT_SECRET not configured');
    }

    // 使用类型断言来避免TypeScript类型检查问题
    return jwt.sign(
      { userId: userId.toString() },
      jwtSecret,
      { expiresIn: jwtExpiresIn } as any
    );
  }

  // 验证是否为有效邮箱格式
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
