#!/usr/bin/env ts-node

/**
 * 试题解析功能测试脚本
 */

import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { DocumentProcessor } from '../services/DocumentProcessor';
import { QuestionParser } from '../services/QuestionParser';

// 加载环境变量
dotenv.config();

async function testQuestionParsing() {
  console.log('🧪 测试试题解析功能...');
  console.log('================================');

  const documentProcessor = new DocumentProcessor();
  const questionParser = new QuestionParser();

  // 测试HTML解析
  console.log('\n📝 测试HTML内容解析...');
  
  const testHtmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试题目</title>
</head>
<body>
    <div class="content">
        1. 以下哪个是JavaScript的数据类型？<br>
        A. string<br>
        B. number<br>
        C. boolean<br>
        D. 以上都是<br>
        <br>
        2. 什么是闭包？<br>
        A. 一种函数<br>
        B. 一种变量<br>
        C. 一种作用域<br>
        D. 函数和其词法环境的组合<br>
        <br>
        3. 请解释JavaScript中的事件循环机制。<br>
        <br>
        4、Vue.js的核心特性包括哪些？<br>
        A）响应式数据绑定<br>
        B）组件化<br>
        C）虚拟DOM<br>
        D）以上都是<br>
    </div>
</body>
</html>`;

  try {
    const questions = await questionParser.parseQuestionsFromHtml(
      testHtmlContent,
      1, // userId
      1  // sourceFileId
    );

    console.log('✅ 解析结果:');
    questions.forEach((question, index) => {
      console.log(`\n题目 ${index + 1}:`);
      console.log(`  题号: ${question.questionNumber}`);
      console.log(`  类型: ${question.questionType}`);
      console.log(`  题干: ${question.stem}`);
      console.log(`  选项数量: ${question.options.length}`);
      if (question.options.length > 0) {
        question.options.forEach((option: string, i: number) => {
          console.log(`    ${option}`);
        });
      }
    });

  } catch (error) {
    console.error('❌ HTML解析测试失败:', error);
  }

  // 测试简单文本解析
  console.log('\n📄 测试简单文本解析...');
  
  const simpleTextHtml = `
<html>
<body>
1. 什么是HTML？
A. 超文本标记语言
B. 编程语言
C. 数据库
D. 操作系统

2. CSS的作用是什么？
A. 控制网页样式
B. 处理数据
C. 编写逻辑
D. 管理数据库

3. 请简述HTTP协议的特点。
</body>
</html>`;

  try {
    const simpleQuestions = await questionParser.parseQuestionsFromHtml(
      simpleTextHtml,
      1,
      2
    );

    console.log('✅ 简单文本解析结果:');
    console.log(`共解析出 ${simpleQuestions.length} 道题目`);
    
    simpleQuestions.forEach((question, index) => {
      console.log(`\n题目 ${index + 1}: ${question.stem.substring(0, 50)}...`);
      console.log(`  类型: ${question.questionType}`);
      console.log(`  选项: ${question.options.length} 个`);
    });

  } catch (error) {
    console.error('❌ 简单文本解析失败:', error);
  }

  // 测试空内容处理
  console.log('\n🔍 测试空内容处理...');
  
  try {
    const emptyQuestions = await questionParser.parseQuestionsFromHtml(
      '<html><body></body></html>',
      1,
      3
    );

    console.log('✅ 空内容处理结果:');
    console.log(`生成题目数量: ${emptyQuestions.length}`);
    if (emptyQuestions.length > 0) {
      console.log(`示例题目: ${emptyQuestions[0].stem}`);
    }

  } catch (error) {
    console.error('❌ 空内容处理失败:', error);
  }

  console.log('\n🎯 测试总结:');
  console.log('- HTML内容解析功能');
  console.log('- 题目格式识别');
  console.log('- 选项提取');
  console.log('- 错误处理机制');
  console.log('- 备用方案生成');
}

// 运行测试
testQuestionParsing().catch(console.error);
