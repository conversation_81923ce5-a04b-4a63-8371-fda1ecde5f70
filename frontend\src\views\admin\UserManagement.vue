<template>
  <Layout>
    <div class="user-management">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>用户管理</h1>
        <p>管理系统中的所有用户账户</p>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filters-section">
        <div class="card">
          <div class="filters">
            <el-select v-model="filters.userType" placeholder="用户类型" clearable @change="loadUsers">
              <el-option label="全部" value="" />
              <el-option label="免费用户" value="free" />
              <el-option label="高级会员" value="premium" />
              <el-option label="管理员" value="admin" />
            </el-select>

            <el-select v-model="filters.isActive" placeholder="账户状态" clearable @change="loadUsers">
              <el-option label="全部" value="" />
              <el-option label="活跃" value="true" />
              <el-option label="禁用" value="false" />
            </el-select>

            <el-input
              v-model="filters.keyword"
              placeholder="搜索用户名或邮箱"
              clearable
              @keyup.enter="loadUsers"
              @clear="loadUsers"
            >
              <template #append>
                <el-button @click="loadUsers">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>

            <el-button @click="loadUsers" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="users-section">
        <div class="card">
          <div class="card-header">
            <h3>用户列表</h3>
            <div class="stats">
              <span>总计: {{ pagination.total }} 个用户</span>
            </div>
          </div>

          <el-table :data="userList" v-loading="loading" stripe>
            <el-table-column prop="id" label="ID" width="80" />
            
            <el-table-column prop="username" label="用户名" min-width="120">
              <template #default="{ row }">
                <div class="user-info">
                  <el-avatar :size="32" :src="row.avatarUrl">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  <span>{{ row.username }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="email" label="邮箱" min-width="200" />

            <el-table-column prop="fullName" label="姓名" min-width="120" />

            <el-table-column prop="userType" label="用户类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getUserTypeColor(row.userType)" size="small">
                  {{ getUserTypeText(row.userType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="isActive" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isActive ? 'success' : 'danger'" size="small">
                  {{ row.isActive ? '活跃' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="isEmailVerified" label="邮箱验证" width="100">
              <template #default="{ row }">
                <el-icon :color="row.isEmailVerified ? '#67c23a' : '#f56c6c'">
                  <component :is="row.isEmailVerified ? 'CircleCheck' : 'CircleClose'" />
                </el-icon>
              </template>
            </el-table-column>

            <el-table-column prop="createdAt" label="注册时间" width="180">
              <template #default="{ row }">
                {{ formatTime(row.createdAt) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="viewUserDetails(row)"
                  >
                    详情
                  </el-button>
                  
                  <el-button
                    :type="row.isActive ? 'warning' : 'success'"
                    size="small"
                    @click="toggleUserStatus(row)"
                  >
                    {{ row.isActive ? '禁用' : '启用' }}
                  </el-button>

                  <el-dropdown @command="(command) => handleUserAction(command, row)">
                    <el-button size="small">
                      更多
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="changeType">修改类型</el-dropdown-item>
                        <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
                        <el-dropdown-item command="viewUsage">使用记录</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadUsers"
              @current-change="loadUsers"
            />
          </div>
        </div>
      </div>

      <!-- 用户详情对话框 -->
      <el-dialog v-model="detailsVisible" title="用户详情" width="600px">
        <div v-if="selectedUser" class="user-details">
          <div class="detail-item">
            <label>用户ID:</label>
            <span>{{ selectedUser.id }}</span>
          </div>
          <div class="detail-item">
            <label>用户名:</label>
            <span>{{ selectedUser.username }}</span>
          </div>
          <div class="detail-item">
            <label>邮箱:</label>
            <span>{{ selectedUser.email }}</span>
          </div>
          <div class="detail-item">
            <label>姓名:</label>
            <span>{{ selectedUser.fullName || '未设置' }}</span>
          </div>
          <div class="detail-item">
            <label>用户类型:</label>
            <el-tag :type="getUserTypeColor(selectedUser.userType)">
              {{ getUserTypeText(selectedUser.userType) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>账户状态:</label>
            <el-tag :type="selectedUser.isActive ? 'success' : 'danger'">
              {{ selectedUser.isActive ? '活跃' : '禁用' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>邮箱验证:</label>
            <el-tag :type="selectedUser.isEmailVerified ? 'success' : 'warning'">
              {{ selectedUser.isEmailVerified ? '已验证' : '未验证' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>注册时间:</label>
            <span>{{ formatTime(selectedUser.createdAt) }}</span>
          </div>
          <div class="detail-item">
            <label>最后更新:</label>
            <span>{{ formatTime(selectedUser.updatedAt) }}</span>
          </div>
          <div v-if="selectedUser.subscriptionExpiresAt" class="detail-item">
            <label>会员到期:</label>
            <span>{{ formatTime(selectedUser.subscriptionExpiresAt) }}</span>
          </div>
        </div>
      </el-dialog>

      <!-- 修改用户类型对话框 -->
      <el-dialog v-model="typeChangeVisible" title="修改用户类型" width="400px">
        <el-form v-if="selectedUser" @submit.prevent="updateUserType">
          <el-form-item label="当前类型">
            <el-tag :type="getUserTypeColor(selectedUser.userType)">
              {{ getUserTypeText(selectedUser.userType) }}
            </el-tag>
          </el-form-item>
          <el-form-item label="新类型">
            <el-select v-model="newUserType" placeholder="选择新的用户类型">
              <el-option label="免费用户" value="free" />
              <el-option label="高级会员" value="premium" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="updateUserType">确认修改</el-button>
            <el-button @click="typeChangeVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Search,
  Refresh,
  User,
  CircleCheck,
  CircleClose,
  ArrowDown
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { http } from '@/utils/http'

// 响应式数据
const loading = ref(false)
const userList = ref([])
const selectedUser = ref(null)
const detailsVisible = ref(false)
const typeChangeVisible = ref(false)
const newUserType = ref('')

const filters = reactive({
  userType: '',
  isActive: '',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      userType: filters.userType || undefined,
      isActive: filters.isActive || undefined,
      keyword: filters.keyword || undefined
    }

    const response = await http.get('/user/all', { params })
    userList.value = response.users
    pagination.total = response.pagination.total
  } catch (error) {
    console.error('Load users error:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查看用户详情
const viewUserDetails = (user: any) => {
  selectedUser.value = user
  detailsVisible.value = true
}

// 切换用户状态
const toggleUserStatus = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要${user.isActive ? '禁用' : '启用'}用户 ${user.username} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await http.put(`/user/${user.id}/status`, {
      isActive: !user.isActive
    })

    ElMessage.success(`用户${user.isActive ? '禁用' : '启用'}成功`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Toggle user status error:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 处理用户操作
const handleUserAction = (command: string, user: any) => {
  selectedUser.value = user
  
  switch (command) {
    case 'changeType':
      newUserType.value = user.userType
      typeChangeVisible.value = true
      break
    case 'resetPassword':
      resetUserPassword(user)
      break
    case 'viewUsage':
      viewUserUsage(user)
      break
  }
}

// 更新用户类型
const updateUserType = async () => {
  try {
    await http.put(`/user/${selectedUser.value.id}/type`, {
      userType: newUserType.value
    })

    ElMessage.success('用户类型修改成功')
    typeChangeVisible.value = false
    loadUsers()
  } catch (error) {
    console.error('Update user type error:', error)
    ElMessage.error('修改用户类型失败')
  }
}

// 重置用户密码
const resetUserPassword = async (user: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户 ${user.username} 的密码吗？新密码将发送到用户邮箱。`,
      '重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 实现重置密码功能
    ElMessage.info('重置密码功能开发中...')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Reset password error:', error)
    }
  }
}

// 查看用户使用记录
const viewUserUsage = (user: any) => {
  // TODO: 实现查看用户使用记录功能
  ElMessage.info('查看使用记录功能开发中...')
}

// 获取用户类型颜色
const getUserTypeColor = (userType: string) => {
  switch (userType) {
    case 'admin': return 'danger'
    case 'premium': return 'warning'
    default: return 'info'
  }
}

// 获取用户类型文本
const getUserTypeText = (userType: string) => {
  switch (userType) {
    case 'admin': return '管理员'
    case 'premium': return '高级会员'
    default: return '免费用户'
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 页面加载时获取用户列表
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.filters-section {
  margin-bottom: 20px;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.filters {
  padding: 20px;
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.card-header {
  padding: 20px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.stats {
  color: #909399;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.actions {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-item label {
  min-width: 80px;
  font-weight: 500;
  color: #606266;
}
</style>
