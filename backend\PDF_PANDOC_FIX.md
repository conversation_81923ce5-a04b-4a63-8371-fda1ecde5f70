# 🔧 PDF转Word Pandoc错误修复

## 🚨 问题描述
```
Unknown input format pdf
Pandoc can convert to PDF, but not from PDF.
```

## 🔍 问题原因
基础版PDF转Word方法直接使用Pandoc处理PDF文件，但Pandoc不支持PDF作为输入格式。

### 错误的原始代码
```typescript
// 错误：直接用Pandoc处理PDF
const command = `pandoc "${inputPath}" -o "${outputPath}"`;
```

Pandoc只能：
- ✅ 从Markdown、HTML、Word等格式转换**到**PDF
- ❌ 不能从PDF格式转换到其他格式

## ✅ 修复方案

### 1. 新的处理流程
```
PDF文件 → pdf-parse提取文本 → 生成HTML → Pandoc转Word
```

### 2. 修复后的代码结构
```typescript
// 新的基础版处理流程
private async convertPdfToWordBasic(inputPath: string, outputPath: string, task: ProcessingTask) {
  // 1. 提取PDF文本
  const textContent = await this.extractPdfText(inputPath);
  
  // 2. 创建Word文档
  await this.createWordDocument(textContent, outputPath);
}
```

### 3. 多层备用方案
1. **主方案**: pdf-parse + Pandoc
2. **备用1**: pdf-parse + 文本文件
3. **备用2**: 错误提示文档

## 🔧 修复内容

### 1. 添加PDF文本提取
```typescript
private async extractPdfText(inputPath: string): Promise<string> {
  try {
    const pdfParse = require('pdf-parse');
    const pdfBuffer = fs.readFileSync(inputPath);
    const data = await pdfParse(pdfBuffer);
    return data.text || '无法提取文本内容';
  } catch (error) {
    // 返回友好的错误提示
    return `PDF解析失败，建议使用高级版功能...`;
  }
}
```

### 2. 添加Word文档生成
```typescript
private async createWordDocument(textContent: string, outputPath: string): Promise<void> {
  // 创建HTML → 用Pandoc转Word → 备用文本文件
}
```

### 3. 添加依赖包
```json
{
  "dependencies": {
    "pdf-parse": "^1.1.1"
  }
}
```

## 📦 安装依赖

### 立即修复
```bash
cd backend
npm install pdf-parse
npm run dev
```

### 可选优化（安装Pandoc）
```bash
# Windows
# 下载并安装 https://pandoc.org/installing.html

# Linux
sudo apt-get install pandoc

# macOS
brew install pandoc
```

## 🎯 修复结果

### 修复前
- ❌ 直接用Pandoc处理PDF
- ❌ 报错：`Unknown input format pdf`
- ❌ 功能完全无法使用

### 修复后
- ✅ 使用pdf-parse提取PDF文本
- ✅ 生成格式化的HTML内容
- ✅ 用Pandoc将HTML转为Word
- ✅ 多层备用方案确保功能可用

## 📊 功能特性

### 基础版PDF转Word现在支持
1. **文本提取**: 从PDF中提取纯文本内容
2. **格式保持**: 保持基本的段落结构
3. **错误处理**: 优雅处理各种错误情况
4. **备用方案**: 确保总能生成可用的输出文件

### 输出文档包含
- 📄 提取的PDF文本内容
- 🕒 转换时间戳
- 📝 格式化的HTML样式
- ⚠️ 必要的使用说明

## 🔍 测试验证

### 1. 上传PDF文件
```bash
curl -X POST http://localhost:3000/api/file/upload/pdf-convert \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.pdf"
```

### 2. 检查处理日志
```
📄 开始提取PDF文本内容...
📝 生成Word文档...
✅ 基础版PDF转Word完成
```

### 3. 验证输出文件
- 文件可以用Word打开
- 包含PDF的文本内容
- 有基本的格式结构

## 🎉 总结

通过这次修复：

1. ✅ **解决核心问题**: Pandoc不能直接处理PDF
2. ✅ **添加正确流程**: PDF → 文本 → HTML → Word
3. ✅ **增强错误处理**: 多层备用方案
4. ✅ **改善用户体验**: 清晰的处理进度和错误提示

现在基础版PDF转Word功能可以正常工作了！虽然效果可能不如高级版，但至少能够提取PDF中的文本内容并生成可用的Word文档。
