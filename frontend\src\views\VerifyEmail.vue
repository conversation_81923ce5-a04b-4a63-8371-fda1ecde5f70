<template>
  <div class="verify-email-container">
    <div class="verify-card">
      <div class="verify-header">
        <el-icon size="64" :color="statusColor">
          <component :is="statusIcon" />
        </el-icon>
        <h1>{{ statusTitle }}</h1>
      </div>
      
      <div class="verify-content">
        <!-- 验证中状态 -->
        <div v-if="status === 'verifying'" class="status-content">
          <p>正在验证您的邮箱地址，请稍候...</p>
          <el-progress :percentage="100" :indeterminate="true" />
        </div>
        
        <!-- 验证成功状态 -->
        <div v-else-if="status === 'success'" class="status-content">
          <p class="success-message">{{ message }}</p>
          <p>您的邮箱已成功验证，现在可以正常使用所有功能了。</p>
          <div class="action-buttons">
            <el-button type="primary" size="large" @click="goToLogin">
              立即登录
            </el-button>
            <el-button size="large" @click="goToHome">
              返回首页
            </el-button>
          </div>
        </div>
        
        <!-- 验证失败状态 -->
        <div v-else-if="status === 'error'" class="status-content">
          <p class="error-message">{{ message }}</p>
          <div class="error-actions">
            <p>可能的原因：</p>
            <ul>
              <li>验证链接已过期（24小时有效期）</li>
              <li>验证链接已被使用</li>
              <li>邮箱地址不正确</li>
            </ul>
            
            <div class="action-buttons">
              <el-button type="primary" @click="showResendForm = true">
                重新发送验证邮件
              </el-button>
              <el-button @click="goToHome">
                返回首页
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 重新发送验证邮件表单 -->
        <div v-if="showResendForm" class="resend-form">
          <el-divider>重新发送验证邮件</el-divider>
          <el-form @submit.prevent="resendVerification">
            <el-form-item label="邮箱地址">
              <el-input
                v-model="resendEmail"
                type="email"
                placeholder="请输入您的邮箱地址"
                :disabled="resending"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="resendVerification"
                :loading="resending"
                :disabled="!resendEmail"
              >
                {{ resending ? '发送中...' : '发送验证邮件' }}
              </el-button>
              <el-button @click="showResendForm = false">
                取消
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  SuccessFilled, 
  WarningFilled, 
  Loading 
} from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'

const route = useRoute()
const router = useRouter()

const status = ref<'verifying' | 'success' | 'error'>('verifying')
const message = ref('')
const showResendForm = ref(false)
const resendEmail = ref('')
const resending = ref(false)

const statusIcon = computed(() => {
  switch (status.value) {
    case 'success': return SuccessFilled
    case 'error': return WarningFilled
    default: return Loading
  }
})

const statusColor = computed(() => {
  switch (status.value) {
    case 'success': return '#67c23a'
    case 'error': return '#f56c6c'
    default: return '#409eff'
  }
})

const statusTitle = computed(() => {
  switch (status.value) {
    case 'success': return '邮箱验证成功'
    case 'error': return '邮箱验证失败'
    default: return '邮箱验证中'
  }
})

const verifyEmail = async () => {
  try {
    const token = route.query.token as string
    const email = route.query.email as string
    
    if (!token || !email) {
      status.value = 'error'
      message.value = '验证链接无效，缺少必要参数'
      resendEmail.value = email || ''
      return
    }
    
    const response = await authApi.verifyEmail(token, email)
    status.value = 'success'
    message.value = response.message
    
    // 3秒后自动跳转到登录页
    setTimeout(() => {
      goToLogin()
    }, 3000)
    
  } catch (error: any) {
    status.value = 'error'
    message.value = error.response?.data?.error || '邮箱验证失败'
    resendEmail.value = (route.query.email as string) || ''
  }
}

const resendVerification = async () => {
  if (!resendEmail.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }
  
  resending.value = true
  try {
    await authApi.resendVerificationEmail(resendEmail.value)
    ElMessage.success('验证邮件已重新发送，请检查您的邮箱')
    showResendForm.value = false
  } catch (error: any) {
    ElMessage.error(error.response?.data?.error || '发送失败，请稍后重试')
  } finally {
    resending.value = false
  }
}

const goToLogin = () => {
  router.push('/login')
}

const goToHome = () => {
  router.push('/')
}

onMounted(() => {
  verifyEmail()
})
</script>

<style scoped>
.verify-email-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.verify-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
  overflow: hidden;
}

.verify-header {
  text-align: center;
  padding: 40px 40px 20px;
  background: #f8fafc;
}

.verify-header h1 {
  margin: 16px 0 0 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.verify-content {
  padding: 20px 40px 40px;
}

.status-content {
  text-align: center;
}

.success-message {
  color: #67c23a;
  font-weight: 500;
  margin-bottom: 16px;
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
  margin-bottom: 16px;
}

.error-actions {
  text-align: left;
  margin-top: 20px;
}

.error-actions ul {
  margin: 16px 0;
  padding-left: 20px;
}

.error-actions li {
  margin-bottom: 8px;
  color: #666;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.resend-form {
  margin-top: 20px;
  text-align: left;
}

@media (max-width: 768px) {
  .verify-card {
    margin: 20px;
  }
  
  .verify-header,
  .verify-content {
    padding: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
