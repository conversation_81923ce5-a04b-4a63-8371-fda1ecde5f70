/**
 * Token自动刷新组合式函数
 * 在应用运行期间自动检查和刷新token
 */

import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { tokenManager } from '@/utils/tokenManager'
import { ElMessage } from 'element-plus'

export function useTokenRefresh() {
  const authStore = useAuthStore()
  const isRefreshing = ref(false)
  let refreshTimer: number | null = null
  let checkTimer: number | null = null

  // 自动刷新token
  const autoRefreshToken = async () => {
    if (isRefreshing.value || !authStore.isAuthenticated) {
      return
    }

    // 检查是否需要刷新
    if (!authStore.shouldRefreshToken()) {
      return
    }

    console.log('🔄 自动刷新token...')
    isRefreshing.value = true

    try {
      const success = await authStore.refreshToken()
      if (success) {
        console.log('✅ Token刷新成功')
      } else {
        console.log('❌ Token刷新失败，需要重新登录')
        ElMessage.warning('登录状态即将过期，请保存工作并重新登录')
      }
    } catch (error) {
      console.error('Token刷新出错:', error)
    } finally {
      isRefreshing.value = false
    }
  }

  // 检查token状态
  const checkTokenStatus = () => {
    if (!authStore.isAuthenticated) {
      return
    }

    // 检查token是否过期
    if (tokenManager.isTokenExpired()) {
      console.log('⚠️ Token已过期')
      authStore.logout()
      return
    }

    // 检查是否需要刷新
    if (tokenManager.shouldRefreshToken()) {
      autoRefreshToken()
    }
  }

  // 启动定时检查
  const startTokenCheck = () => {
    // 每30秒检查一次token状态
    checkTimer = window.setInterval(checkTokenStatus, 30 * 1000)
    
    // 立即检查一次
    checkTokenStatus()
  }

  // 停止定时检查
  const stopTokenCheck = () => {
    if (checkTimer) {
      clearInterval(checkTimer)
      checkTimer = null
    }
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  // 获取token状态信息
  const getTokenStatus = () => {
    return {
      isExpired: tokenManager.isTokenExpired(),
      shouldRefresh: tokenManager.shouldRefreshToken(),
      remainingTime: tokenManager.getTokenRemainingTimeFormatted(),
      isRefreshing: isRefreshing.value
    }
  }

  // 手动刷新token
  const manualRefreshToken = async () => {
    if (isRefreshing.value) {
      ElMessage.info('正在刷新token，请稍候...')
      return
    }

    await autoRefreshToken()
  }

  // 页面可见性变化时检查token
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      checkTokenStatus()
    }
  }

  onMounted(() => {
    startTokenCheck()
    document.addEventListener('visibilitychange', handleVisibilityChange)
  })

  onUnmounted(() => {
    stopTokenCheck()
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })

  return {
    isRefreshing: readonly(isRefreshing),
    getTokenStatus,
    manualRefreshToken,
    startTokenCheck,
    stopTokenCheck
  }
}

// 全局token刷新实例
let globalTokenRefresh: ReturnType<typeof useTokenRefresh> | null = null

export function useGlobalTokenRefresh() {
  if (!globalTokenRefresh) {
    globalTokenRefresh = useTokenRefresh()
  }
  return globalTokenRefresh
}
