import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { FileController } from '../controllers/FileController';
import { authenticateToken, checkFeatureUsage } from '../middleware/auth';

const router = Router();
const fileController = new FileController();

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../../uploads'));
  },
  filename: (req, file, cb) => {
    // 确保原始文件名正确编码
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    const ext = path.extname(originalName);
    const filename = `${uuidv4()}${ext}`;

    // 将正确编码的原始文件名存储到file对象中
    file.originalname = originalName;

    cb(null, filename);
  }
});

// 文件过滤器
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword'
  ];
  
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，仅支持PDF和Word文档'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 5 // 最多5个文件
  }
});

// PDF转Word上传
router.post('/upload/pdf-convert', 
  authenticateToken,
  checkFeatureUsage('pdfConvert'),
  upload.single('file'),
  fileController.uploadForPdfConvert.bind(fileController)
);

// 试题解析上传
router.post('/upload/question-parse',
  authenticateToken,
  checkFeatureUsage('questionParse'),
  upload.single('file'),
  fileController.uploadForQuestionParse.bind(fileController)
);

// 批量上传
router.post('/upload/batch',
  authenticateToken,
  upload.array('files', 5),
  fileController.batchUpload.bind(fileController)
);

// 获取用户上传的文件列表
router.get('/uploads',
  authenticateToken,
  fileController.getUserUploads.bind(fileController)
);

// 获取文件详情
router.get('/uploads/:fileId',
  authenticateToken,
  fileController.getFileDetails.bind(fileController)
);

// 下载文件
router.get('/download/:fileId',
  authenticateToken,
  fileController.downloadFile.bind(fileController)
);

// 删除文件
router.delete('/uploads/:fileId',
  authenticateToken,
  fileController.deleteFile.bind(fileController)
);

// 富文本编辑器图片上传
router.post('/upload/image',
  authenticateToken,
  upload.single('image'),
  fileController.uploadImage.bind(fileController)
);

export default router;
