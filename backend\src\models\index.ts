// 导出所有模型
export { User } from './User';
export { Question } from './Question';
export { FileUpload } from './FileUpload';
export { ProcessingTask } from './ProcessingTask';
export { Exam } from './Exam';

// 导出类型
export type { UserAttributes, UserCreationAttributes, UserType, DailyUsage } from './User';
export type { QuestionAttributes, QuestionCreationAttributes, QuestionType, QuestionOption } from './Question';
export type { FileUploadAttributes, FileUploadCreationAttributes, UploadType, FileStatus } from './FileUpload';
export type { ProcessingTaskAttributes, ProcessingTaskCreationAttributes, TaskType, TaskStatus } from './ProcessingTask';
export type { ExamAttributes, ExamCreationAttributes, ExamType, ExamQuestion } from './Exam';

// 导出数据库连接
export { sequelize, connectDatabase } from '../config/database';
