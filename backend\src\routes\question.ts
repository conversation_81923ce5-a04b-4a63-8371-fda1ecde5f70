import { Router } from 'express';
import { QuestionController } from '../controllers/QuestionController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const questionController = new QuestionController();

// 获取题库列表
router.get('/', authenticateToken, questionController.getQuestions.bind(questionController));

// 获取题目详情
router.get('/:questionId', authenticateToken, questionController.getQuestionById.bind(questionController));

// 创建题目
router.post('/', authenticateToken, questionController.createQuestion.bind(questionController));

// 更新题目
router.put('/:questionId', authenticateToken, questionController.updateQuestion.bind(questionController));

// 删除题目
router.delete('/:questionId', authenticateToken, questionController.deleteQuestion.bind(questionController));

// 批量删除题目
router.delete('/batch', authenticateToken, questionController.batchDeleteQuestions.bind(questionController));

// 搜索题目
router.get('/search', authenticateToken, questionController.searchQuestions.bind(questionController));

// 按标签获取题目
router.get('/tags/:tag', authenticateToken, questionController.getQuestionsByTag.bind(questionController));

// 按学科获取题目
router.get('/subject/:subject', authenticateToken, questionController.getQuestionsBySubject.bind(questionController));

// 按难度获取题目
router.get('/difficulty/:level', authenticateToken, questionController.getQuestionsByDifficulty.bind(questionController));

// 获取题目统计信息
router.get('/stats/overview', authenticateToken, questionController.getQuestionStats.bind(questionController));

export default router;
