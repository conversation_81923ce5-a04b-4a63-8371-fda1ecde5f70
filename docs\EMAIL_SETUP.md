# 邮箱验证配置指南

## 概述

d2x 现在支持用户注册时的邮箱验证功能，确保用户邮箱的真实性和账号安全。

## 功能特性

- ✅ 用户注册后自动发送验证邮件
- ✅ 精美的HTML邮件模板
- ✅ 验证链接24小时有效期
- ✅ 支持重新发送验证邮件
- ✅ 验证成功后发送欢迎邮件
- ✅ 未验证用户无法登录

## 邮件服务配置

### 1. Gmail 配置（推荐）

#### 步骤1：启用两步验证
1. 登录 Google 账号
2. 前往 [Google 账号安全设置](https://myaccount.google.com/security)
3. 启用"两步验证"

#### 步骤2：生成应用专用密码
1. 在安全设置中找到"应用专用密码"
2. 选择"邮件"和"其他（自定义名称）"
3. 输入"d2x"作为应用名称
4. 复制生成的16位密码

#### 步骤3：配置环境变量
```env
# Gmail SMTP 配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-digit-app-password
SMTP_FROM=<EMAIL>
FRONTEND_URL=http://localhost:5173
```

### 2. 其他邮件服务商

#### QQ邮箱
```env
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-authorization-code
```

#### 163邮箱
```env
SMTP_HOST=smtp.163.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-authorization-code
```

#### 企业邮箱（腾讯企业邮）
```env
SMTP_HOST=smtp.exmail.qq.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
```

### 3. 专业邮件服务

#### SendGrid
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
```

#### Mailgun
```env
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your-mailgun-username
SMTP_PASS=your-mailgun-password
```

## 测试邮件配置

### 1. 后端测试
```bash
cd backend
npm run test-email
```

### 2. 手动测试
1. 启动后端服务
2. 注册一个新用户
3. 检查控制台日志是否显示邮件发送成功
4. 检查邮箱是否收到验证邮件

## 邮件模板自定义

邮件模板位于 `backend/src/services/EmailService.ts`，包含：

- **验证邮件模板** (`getEmailVerificationTemplate`)
- **密码重置模板** (`getPasswordResetTemplate`)
- **欢迎邮件模板** (`getWelcomeTemplate`)

### 自定义样式
```typescript
// 修改邮件样式
const customStyle = `
  .header { 
    background: linear-gradient(135deg, #your-color1, #your-color2);
  }
  .button { 
    background: #your-brand-color;
  }
`;
```

## 故障排除

### 常见问题

#### 1. 邮件发送失败
**错误**: `Error: Invalid login`
**解决**: 
- 检查用户名和密码是否正确
- 确认已启用应用专用密码（Gmail）
- 检查邮箱是否启用SMTP服务

#### 2. 邮件被标记为垃圾邮件
**解决**:
- 配置SPF记录
- 使用专业邮件服务（SendGrid、Mailgun）
- 添加DKIM签名

#### 3. 验证链接无效
**原因**:
- 链接已过期（24小时）
- 用户已验证过
- 数据库中令牌不匹配

#### 4. 邮件发送慢
**解决**:
- 使用专业邮件服务
- 配置连接池
- 异步发送邮件

### 调试技巧

#### 1. 启用详细日志
```env
NODE_ENV=development
```

#### 2. 测试SMTP连接
```javascript
// 在 EmailService 中添加
await emailService.testConnection();
```

#### 3. 检查邮件队列
```bash
# 查看Redis队列（如果使用）
redis-cli
LLEN email_queue
```

## 生产环境建议

### 1. 使用专业邮件服务
- **SendGrid**: 免费额度100封/天
- **Mailgun**: 免费额度5000封/月
- **Amazon SES**: 按量付费，成本低

### 2. 配置域名验证
```dns
# SPF记录
TXT @ "v=spf1 include:_spf.google.com ~all"

# DKIM记录
TXT google._domainkey "v=DKIM1; k=rsa; p=your-public-key"
```

### 3. 监控邮件发送
- 配置邮件发送失败告警
- 监控邮件送达率
- 记录用户验证行为

### 4. 安全考虑
- 定期轮换SMTP密码
- 使用环境变量存储敏感信息
- 限制验证邮件发送频率

## API 接口

### 验证邮箱
```http
GET /api/auth/verify-email?token=xxx&email=xxx
```

### 重新发送验证邮件
```http
POST /api/auth/resend-verification
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

## 前端集成

### 验证页面路由
```javascript
{
  path: '/verify-email',
  component: () => import('@/views/VerifyEmail.vue')
}
```

### 处理验证失败
```javascript
// 登录时检查邮箱验证状态
if (error.response?.data?.code === 'EMAIL_NOT_VERIFIED') {
  // 显示重新发送验证邮件选项
}
```

---

配置完成后，用户注册流程将变为：
1. 用户填写注册信息
2. 系统创建账号（未激活状态）
3. 发送验证邮件
4. 用户点击邮件中的验证链接
5. 账号激活，可以正常登录
