import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import { ProcessingTask } from '../models/ProcessingTask';

const execAsync = promisify(exec);

export class ExamGenerator {
  
  // 生成试卷Word文档
  async generateExamDocument(taskId: string, exam: any): Promise<void> {
    const task = await ProcessingTask.findByPk(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }
    
    try {
      // 更新任务状态
      task.updateProgress(10, 'processing');
      await task.save();
      
      // 生成HTML内容
      const htmlContent = await this.generateExamHTML(exam);
      
      task.updateProgress(50);
      await task.save();
      
      // 保存HTML到临时文件
      const tempDir = path.join(__dirname, '../../temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      const htmlFilename = `exam_${exam.id}_${Date.now()}.html`;
      const htmlPath = path.join(tempDir, htmlFilename);
      fs.writeFileSync(htmlPath, htmlContent, 'utf-8');
      
      task.updateProgress(70);
      await task.save();
      
      // 使用Pandoc转换为Word文档
      const outputFilename = `${exam.title.replace(/[^\w\s]/gi, '')}_${Date.now()}.docx`;
      const outputPath = path.join(tempDir, outputFilename);
      
      const command = `pandoc "${htmlPath}" -o "${outputPath}" --reference-doc="${this.getTemplateDocPath()}"`;
      
      await execAsync(command);
      
      // 清理HTML临时文件
      fs.unlinkSync(htmlPath);
      
      task.updateProgress(90);
      await task.save();
      
      // 更新任务结果
      task.markCompleted({
        outputPath,
        outputFilename,
        examId: exam.id,
        examTitle: exam.title
      });
      
      await task.save();
      
    } catch (error) {
      console.error('试卷生成失败:', error);
      task.markFailed(error instanceof Error ? error.message : '生成失败');
      await task.save();
    }
  }
  
  // 生成试卷HTML内容
  async generateExamHTML(exam: any): Promise<string> {
    const questions = exam.questions;
    
    let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${exam.title}</title>
    <style>
        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 2cm;
            color: #333;
        }
        .exam-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .exam-title {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .exam-info {
            font-size: 11pt;
            margin: 5px 0;
        }
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            margin: 25px 0 15px 0;
            border-bottom: 1px solid #666;
            padding-bottom: 5px;
        }
        .question {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
        .question-header {
            font-weight: bold;
            margin-bottom: 8px;
        }
        .question-stem {
            margin-bottom: 10px;
            text-indent: 0;
        }
        .question-options {
            margin-left: 20px;
        }
        .option {
            margin: 5px 0;
        }
        .answer-area {
            border-bottom: 1px solid #ccc;
            min-height: 30px;
            margin: 10px 0;
        }
        .page-break {
            page-break-before: always;
        }
        @media print {
            body { margin: 1.5cm; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="exam-header">
        <div class="exam-title">${exam.title}</div>
        ${exam.subject ? `<div class="exam-info">学科：${exam.subject}</div>` : ''}
        ${exam.timeLimit ? `<div class="exam-info">考试时间：${exam.timeLimit}分钟</div>` : ''}
        <div class="exam-info">总分：${exam.totalScore}分</div>
        ${exam.instructions ? `<div class="exam-info">说明：${exam.instructions}</div>` : ''}
    </div>
`;
    
    // 按大题分组
    const sections = this.groupQuestionsBySection(questions);
    
    for (const [sectionTitle, sectionQuestions] of sections) {
      if (sectionTitle) {
        html += `    <div class="section-title">${sectionTitle}</div>\n`;
      }
      
      for (let i = 0; i < sectionQuestions.length; i++) {
        const questionData = sectionQuestions[i];
        const question = questionData.questionId as any;
        const questionNumber = questionData.questionOrder || (i + 1);
        
        html += `    <div class="question">\n`;
        html += `        <div class="question-header">${questionNumber}. (${questionData.score}分)</div>\n`;
        html += `        <div class="question-stem">${question.stem}</div>\n`;
        
        // 添加选项
        if (question.options && question.options.length > 0) {
          html += `        <div class="question-options">\n`;
          for (const option of question.options) {
            html += `            <div class="option">${option.key}. ${option.content}</div>\n`;
          }
          html += `        </div>\n`;
        } else {
          // 非选择题添加答题区域
          html += `        <div class="answer-area"></div>\n`;
        }
        
        html += `    </div>\n`;
      }
    }
    
    html += `
</body>
</html>`;
    
    return html;
  }
  
  // 按大题分组题目
  private groupQuestionsBySection(questions: any[]): Map<string, any[]> {
    const sections = new Map<string, any[]>();
    
    for (const question of questions) {
      const sectionTitle = question.sectionTitle || '题目';
      
      if (!sections.has(sectionTitle)) {
        sections.set(sectionTitle, []);
      }
      
      sections.get(sectionTitle)!.push(question);
    }
    
    return sections;
  }
  
  // 获取Word模板文档路径
  private getTemplateDocPath(): string {
    const templatePath = path.join(__dirname, '../../templates/exam_template.docx');
    
    // 如果模板不存在，创建一个基础模板
    if (!fs.existsSync(templatePath)) {
      this.createDefaultTemplate(templatePath);
    }
    
    return templatePath;
  }
  
  // 创建默认Word模板
  private createDefaultTemplate(templatePath: string): void {
    const templateDir = path.dirname(templatePath);
    if (!fs.existsSync(templateDir)) {
      fs.mkdirSync(templateDir, { recursive: true });
    }
    
    // 这里应该创建一个基础的Word模板文件
    // 由于创建Word文件比较复杂，这里暂时跳过
    // 实际项目中可以使用现有的模板文件
    console.log('Word模板文件不存在，请手动创建:', templatePath);
  }
  
  // 生成答题卡HTML
  async generateAnswerSheetHTML(exam: any): Promise<string> {
    const questions = exam.questions;
    
    let html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${exam.title} - 答题卡</title>
    <style>
        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 12pt;
            line-height: 1.6;
            margin: 2cm;
        }
        .answer-sheet-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .answer-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .answer-item {
            border: 1px solid #333;
            padding: 10px;
            text-align: center;
        }
        .question-number {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .answer-options {
            display: flex;
            justify-content: space-around;
        }
        .option-circle {
            width: 20px;
            height: 20px;
            border: 2px solid #333;
            border-radius: 50%;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="answer-sheet-header">
        <div class="exam-title">${exam.title} - 答题卡</div>
        <div>姓名：_____________ 学号：_____________ 班级：_____________</div>
    </div>
    
    <div class="answer-grid">
`;
    
    // 生成选择题答题区域
    const choiceQuestions = questions.filter((q: any) => {
      const question = q.questionId as any;
      return ['single_choice', 'multiple_choice', 'true_false'].includes(question.questionType);
    });
    
    for (const questionData of choiceQuestions) {
      const question = questionData.questionId as any;
      const questionNumber = questionData.questionOrder;
      
      html += `        <div class="answer-item">\n`;
      html += `            <div class="question-number">${questionNumber}</div>\n`;
      html += `            <div class="answer-options">\n`;
      
      if (question.options) {
        for (const option of question.options) {
          html += `                <div>${option.key} <span class="option-circle"></span></div>\n`;
        }
      }
      
      html += `            </div>\n`;
      html += `        </div>\n`;
    }
    
    html += `
    </div>
</body>
</html>`;
    
    return html;
  }
}
