/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 布局样式 */
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 250px;
  background: #fff;
  border-right: 1px solid #e8eaec;
  overflow-y: auto;
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: #f5f7fa;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e8eaec;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 按钮样式 */
.btn-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* 上传区域样式 */
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-area.dragover {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
}

.upload-hint {
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

/* 进度条样式 */
.progress-container {
  margin: 20px 0;
}

.progress-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.progress-label {
  width: 120px;
  font-size: 14px;
  color: #606266;
}

.progress-bar {
  flex: 1;
  margin: 0 10px;
}

.progress-status {
  width: 60px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

/* 题目卡片样式 */
.question-card {
  background: #fff;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s;
}

.question-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.question-number {
  font-weight: 600;
  color: #409eff;
  margin-right: 8px;
}

.question-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  background: #f0f2f5;
  color: #666;
}

.question-stem {
  line-height: 1.6;
  color: #333;
  margin-bottom: 12px;
}

.question-options {
  margin-bottom: 12px;
}

.question-option {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 4px 0;
}

.option-key {
  font-weight: 600;
  margin-right: 8px;
  min-width: 20px;
}

.option-content {
  flex: 1;
  line-height: 1.5;
}

.question-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #999;
}

.question-tags {
  display: flex;
  gap: 4px;
}

.question-tag {
  padding: 2px 6px;
  background: #f0f9ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sidebar {
    width: 200px;
  }
  
  .layout-content {
    padding: 10px;
  }
  
  .card {
    padding: 15px;
  }
  
  .btn-group {
    flex-direction: column;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .layout-main {
    flex-direction: column;
  }
  
  .layout-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e8eaec;
  }
  
  .upload-area {
    padding: 20px;
  }
  
  .upload-icon {
    font-size: 32px;
  }
}
