import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';
import { FileUpload } from './FileUpload';

// 任务类型
export type TaskType = 'pdf_to_word' | 'question_parsing' | 'exam_generation';

// 任务状态
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed';

// 任务属性接口
export interface ProcessingTaskAttributes {
  id: number;
  userId: number;
  fileUploadId?: number;
  taskType: TaskType;
  status: TaskStatus;
  progress: number; // 0-100
  errorMessage?: string;
  resultData?: any; // JSON数据，存储任务结果
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 创建任务时的可选属性
export interface ProcessingTaskCreationAttributes extends Optional<ProcessingTaskAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// ProcessingTask模型类
class ProcessingTask extends Model<ProcessingTaskAttributes, ProcessingTaskCreationAttributes> implements ProcessingTaskAttributes {
  public id!: number;
  public userId!: number;
  public fileUploadId?: number;
  public taskType!: TaskType;
  public status!: TaskStatus;
  public progress!: number;
  public errorMessage?: string;
  public resultData?: any;
  public startedAt?: Date;
  public completedAt?: Date;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联的用户和文件
  public User?: User;
  public FileUpload?: FileUpload;

  // 开始任务
  public async startTask(): Promise<void> {
    this.status = 'processing';
    this.startedAt = new Date();
    this.progress = 0;
    await this.save();
  }

  // 更新进度
  public async updateProgress(progress: number, status?: TaskStatus): Promise<void> {
    this.progress = Math.min(100, Math.max(0, progress));
    if (status) {
      this.status = status;
    }
    await this.save();
  }

  // 标记任务完成
  public async markCompleted(resultData?: any): Promise<void> {
    this.status = 'completed';
    this.progress = 100;
    this.completedAt = new Date();
    if (resultData) {
      this.resultData = resultData;
    }
    await this.save();
  }

  // 标记任务失败
  public async markFailed(errorMessage: string): Promise<void> {
    this.status = 'failed';
    this.errorMessage = errorMessage;
    this.completedAt = new Date();
    await this.save();
  }

  // 获取任务执行时长（毫秒）
  public getExecutionTime(): number | null {
    if (!this.startedAt) return null;
    
    const endTime = this.completedAt || new Date();
    return endTime.getTime() - this.startedAt.getTime();
  }

  // 获取任务类型的中文名称
  public getTaskTypeName(): string {
    const typeNames = {
      pdf_to_word: 'PDF转Word',
      question_parsing: '试题解析',
      exam_generation: '试卷生成'
    };
    return typeNames[this.taskType] || '未知任务';
  }

  // 获取状态的中文名称
  public getStatusName(): string {
    const statusNames = {
      pending: '等待中',
      processing: '处理中',
      completed: '已完成',
      failed: '失败'
    };
    return statusNames[this.status] || '未知状态';
  }

  // 检查任务是否可以重试
  public canRetry(): boolean {
    return this.status === 'failed';
  }

  // 检查任务是否可以取消
  public canCancel(): boolean {
    return ['pending', 'processing'].includes(this.status);
  }

  // 重置任务状态（用于重试）
  public async resetForRetry(): Promise<void> {
    this.status = 'pending';
    this.progress = 0;
    this.errorMessage = undefined;
    this.startedAt = undefined;
    this.completedAt = undefined;
    await this.save();
  }
}

// 定义模型
ProcessingTask.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  fileUploadId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'file_uploads',
      key: 'id'
    }
  },
  taskType: {
    type: DataTypes.ENUM('pdf_to_word', 'question_parsing', 'exam_generation'),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'pending'
  },
  progress: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  resultData: {
    type: DataTypes.JSON,
    allowNull: true
  },
  startedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'ProcessingTask',
  tableName: 'processing_tasks',
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['fileUploadId']
    },
    {
      fields: ['taskType']
    },
    {
      fields: ['status']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// 定义关联关系
ProcessingTask.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

ProcessingTask.belongsTo(FileUpload, {
  foreignKey: 'fileUploadId',
  as: 'fileUpload'
});

User.hasMany(ProcessingTask, {
  foreignKey: 'userId',
  as: 'processingTasks'
});

FileUpload.hasMany(ProcessingTask, {
  foreignKey: 'fileUploadId',
  as: 'processingTasks'
});

export { ProcessingTask };
export default ProcessingTask;
