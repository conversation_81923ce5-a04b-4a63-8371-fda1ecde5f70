@echo off
chcp 65001 >nul

echo 🚀 d2x 智能试题解析平台启动脚本
echo ==================================

REM 检查Node.js
echo 📋 检查环境...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 16+ 版本
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node -v') do set NODE_MAJOR=%%a
set NODE_MAJOR=%NODE_MAJOR:v=%
if %NODE_MAJOR% lss 16 (
    echo ❌ Node.js 版本过低，需要 16+ 版本
    node -v
    pause
    exit /b 1
)

echo ✅ Node.js 版本: 
node -v

REM 检查MySQL
where mysql >nul 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  MySQL 未安装或未在PATH中，请确保MySQL正在运行
) else (
    echo ✅ MySQL 已安装
)

echo.
echo 📦 安装依赖...

echo 安装后端依赖...
cd backend
if not exist "node_modules" (
    call npm install
) else (
    echo 后端依赖已存在，跳过安装
)

echo 安装前端依赖...
cd ..\frontend
if not exist "node_modules" (
    call npm install
) else (
    echo 前端依赖已存在，跳过安装
)

cd ..

echo.
echo 🔧 检查配置...

if not exist "backend\.env" (
    echo 创建后端环境配置...
    copy "backend\.env.example" "backend\.env" >nul
    echo ✅ 已创建 backend\.env
    echo ⚠️  请配置以下重要设置：
    echo    - MySQL数据库连接信息
    echo    - SMTP邮件服务配置（用于邮箱验证）
    echo    - 详细配置说明请查看 docs\EMAIL_SETUP.md
) else (
    echo ✅ 后端环境配置已存在
)

if not exist "frontend\.env.development" (
    echo 创建前端环境配置...
    copy "frontend\.env.example" "frontend\.env.development" >nul
    echo ✅ 已创建 frontend\.env.development
) else (
    echo ✅ 前端环境配置已存在
)

echo.
echo 🚀 启动服务...
echo 请在不同的命令提示符窗口中运行以下命令：
echo.
echo 1. 启动后端服务：
echo    cd backend ^&^& npm run dev
echo.
echo 2. 启动前端服务：
echo    cd frontend ^&^& npm run dev
echo.
echo 3. 访问应用：
echo    http://localhost:5173
echo.
echo 📚 默认管理员账号：
echo    用户名: admin
echo    密码: admin123
echo.
echo 📧 邮件配置检查：
echo    运行 npm run check-email 检查邮件配置
echo    运行 npm run test-email 测试邮件发送
echo.
echo 📚 更多信息请查看 PROJECT_README.md
echo.

set /p choice="是否自动启动后端服务？(y/n): "
if /i "%choice%"=="y" (
    echo 启动后端服务...
    start "d2x Backend" cmd /k "cd backend && npm run dev"
    
    echo 等待3秒后启动前端服务...
    timeout /t 3 /nobreak >nul
    
    echo 启动前端服务...
    start "d2x Frontend" cmd /k "cd frontend && npm run dev"
    
    echo.
    echo ✅ 服务已启动！
    echo 后端服务: http://localhost:3000
    echo 前端服务: http://localhost:5173
)

pause
