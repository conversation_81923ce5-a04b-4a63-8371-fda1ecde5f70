/**
 * Token管理工具
 * 处理token的存储、验证和自动刷新
 */

import { ElMessage } from 'element-plus'
import router from '@/router'

interface TokenInfo {
  token: string
  expiresAt: number
  refreshToken?: string
}

class TokenManager {
  private readonly TOKEN_KEY = 'auth_token'
  private readonly TOKEN_INFO_KEY = 'token_info'
  private readonly REFRESH_THRESHOLD = 5 * 60 * 1000 // 5分钟前刷新

  /**
   * 设置token信息
   */
  setToken(token: string, expiresIn: string = '1d'): void {
    const expiresAt = this.calculateExpiresAt(expiresIn)
    
    const tokenInfo: TokenInfo = {
      token,
      expiresAt
    }
    
    localStorage.setItem(this.TOKEN_KEY, token)
    localStorage.setItem(this.TOKEN_INFO_KEY, JSON.stringify(tokenInfo))
  }

  /**
   * 获取token
   */
  getToken(): string | null {
    const token = localStorage.getItem(this.TOKEN_KEY)
    
    if (!token) {
      return null
    }
    
    // 检查token是否过期
    if (this.isTokenExpired()) {
      this.clearToken()
      return null
    }
    
    return token
  }

  /**
   * 获取token信息
   */
  getTokenInfo(): TokenInfo | null {
    const tokenInfoStr = localStorage.getItem(this.TOKEN_INFO_KEY)
    
    if (!tokenInfoStr) {
      return null
    }
    
    try {
      return JSON.parse(tokenInfoStr)
    } catch {
      return null
    }
  }

  /**
   * 检查token是否过期
   */
  isTokenExpired(): boolean {
    const tokenInfo = this.getTokenInfo()
    
    if (!tokenInfo) {
      return true
    }
    
    return Date.now() >= tokenInfo.expiresAt
  }

  /**
   * 检查是否需要刷新token
   */
  shouldRefreshToken(): boolean {
    const tokenInfo = this.getTokenInfo()
    
    if (!tokenInfo) {
      return false
    }
    
    return Date.now() >= (tokenInfo.expiresAt - this.REFRESH_THRESHOLD)
  }

  /**
   * 清除token
   */
  clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.TOKEN_INFO_KEY)
  }

  /**
   * 处理token过期
   */
  handleTokenExpired(): void {
    this.clearToken()
    
    // 避免重复提示
    if (router.currentRoute.value.path !== '/login') {
      ElMessage.error('登录已过期，请重新登录')
      router.push('/login')
    }
  }

  /**
   * 计算过期时间
   */
  private calculateExpiresAt(expiresIn: string): number {
    const now = Date.now()
    
    // 解析过期时间字符串 (如: "1d", "7d", "24h", "60m")
    const match = expiresIn.match(/^(\d+)([dhm])$/)
    
    if (!match) {
      // 默认1天
      return now + 24 * 60 * 60 * 1000
    }
    
    const [, value, unit] = match
    const numValue = parseInt(value, 10)
    
    switch (unit) {
      case 'd': // 天
        return now + numValue * 24 * 60 * 60 * 1000
      case 'h': // 小时
        return now + numValue * 60 * 60 * 1000
      case 'm': // 分钟
        return now + numValue * 60 * 1000
      default:
        return now + 24 * 60 * 60 * 1000
    }
  }

  /**
   * 获取token剩余时间（毫秒）
   */
  getTokenRemainingTime(): number {
    const tokenInfo = this.getTokenInfo()
    
    if (!tokenInfo) {
      return 0
    }
    
    return Math.max(0, tokenInfo.expiresAt - Date.now())
  }

  /**
   * 获取token剩余时间（可读格式）
   */
  getTokenRemainingTimeFormatted(): string {
    const remaining = this.getTokenRemainingTime()
    
    if (remaining <= 0) {
      return '已过期'
    }
    
    const hours = Math.floor(remaining / (60 * 60 * 1000))
    const minutes = Math.floor((remaining % (60 * 60 * 1000)) / (60 * 1000))
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }
}

export const tokenManager = new TokenManager()
export default tokenManager
