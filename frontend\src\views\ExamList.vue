<template>
  <Layout>
    <div class="exam-list">
      <div class="page-header">
        <div class="header-left">
          <h2>试卷列表</h2>
          <p>管理您创建的试卷，支持预览、编辑和生成</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="$router.push('/exam-builder')">
            <el-icon><Plus /></el-icon>
            创建试卷
          </el-button>
        </div>
      </div>

      <div class="exam-grid" v-loading="loading">
        <div
          v-for="exam in examList"
          :key="exam.id"
          class="exam-card"
        >
          <div class="exam-header">
            <h3 class="exam-title">{{ exam.title }}</h3>
            <el-dropdown @command="handleCommand">
              <el-button type="text">
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`preview_${exam.id}`">预览</el-dropdown-item>
                  <el-dropdown-item :command="`edit_${exam.id}`">编辑</el-dropdown-item>
                  <el-dropdown-item :command="`generate_${exam.id}`">生成Word</el-dropdown-item>
                  <el-dropdown-item :command="`duplicate_${exam.id}`">复制</el-dropdown-item>
                  <el-dropdown-item :command="`delete_${exam.id}`" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="exam-info">
            <div class="info-item">
              <el-icon><Collection /></el-icon>
              <span>{{ exam.questions?.length || 0 }} 道题目</span>
            </div>
            <div class="info-item">
              <el-icon><Trophy /></el-icon>
              <span>{{ exam.totalScore }} 分</span>
            </div>
            <div class="info-item" v-if="exam.timeLimit">
              <el-icon><Clock /></el-icon>
              <span>{{ exam.timeLimit }} 分钟</span>
            </div>
            <div class="info-item" v-if="exam.subject">
              <el-icon><Reading /></el-icon>
              <span>{{ exam.subject }}</span>
            </div>
          </div>
          
          <div class="exam-description" v-if="exam.description">
            {{ exam.description }}
          </div>
          
          <div class="exam-footer">
            <span class="create-time">{{ formatTime(exam.createdAt) }}</span>
            <div class="exam-actions">
              <el-button size="small" @click="previewExam(exam.id)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <el-button type="primary" size="small" @click="generateExam(exam.id)">
                <el-icon><Document /></el-icon>
                生成
              </el-button>
            </div>
          </div>
        </div>
        
        <el-empty v-if="!loading && examList.length === 0" description="暂无试卷数据">
          <el-button type="primary" @click="$router.push('/exam-builder')">
            创建第一份试卷
          </el-button>
        </el-empty>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[12, 24, 48]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadExams"
          @current-change="loadExams"
        />
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Plus,
  MoreFilled,
  Collection,
  Trophy,
  Clock,
  Reading,
  View,
  Document
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import { http } from '@/utils/http'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const examList = ref([])

const pagination = ref({
  page: 1,
  limit: 12,
  total: 0
})

const loadExams = async () => {
  loading.value = true
  try {
    const response = await http.get('/exam', {
      params: {
        page: pagination.value.page,
        limit: pagination.value.limit
      }
    })
    
    examList.value = response.exams || []
    pagination.value.total = response.pagination?.total || 0
  } catch (error) {
    console.error('Load exams error:', error)
    ElMessage.error('加载试卷列表失败')
  } finally {
    loading.value = false
  }
}

const handleCommand = (command: string) => {
  const [action, examId] = command.split('_')
  
  switch (action) {
    case 'preview':
      previewExam(examId)
      break
    case 'edit':
      editExam(examId)
      break
    case 'generate':
      generateExam(examId)
      break
    case 'duplicate':
      duplicateExam(examId)
      break
    case 'delete':
      deleteExam(examId)
      break
  }
}

const previewExam = async (examId: string) => {
  try {
    const response = await http.get(`/exam/${examId}/preview`)
    // 这里可以打开预览对话框或新窗口
    ElMessage.info('预览功能开发中...')
  } catch (error) {
    console.error('Preview exam error:', error)
    ElMessage.error('预览试卷失败')
  }
}

const editExam = (examId: string) => {
  router.push(`/exam-builder?id=${examId}`)
}

const generateExam = async (examId: string) => {
  if (!authStore.canUseFeature('examGenerate')) {
    ElMessage.warning('今日生成次数已达上限，请升级到高级会员')
    return
  }
  
  try {
    const response = await http.post(`/exam/${examId}/generate`)
    ElMessage.success('试卷生成任务已创建，请稍后查看任务中心')
    
    // 可以跳转到任务中心
    router.push('/tasks')
  } catch (error) {
    console.error('Generate exam error:', error)
    ElMessage.error('生成试卷失败')
  }
}

const duplicateExam = async (examId: string) => {
  try {
    await http.post(`/exam/${examId}/duplicate`)
    ElMessage.success('试卷复制成功')
    loadExams()
  } catch (error) {
    console.error('Duplicate exam error:', error)
    ElMessage.error('复制试卷失败')
  }
}

const deleteExam = async (examId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这份试卷吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await http.delete(`/exam/${examId}`)
    ElMessage.success('试卷删除成功')
    loadExams()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete exam error:', error)
      ElMessage.error('删除试卷失败')
    }
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadExams()
})
</script>

<style scoped>
.exam-list {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.header-left h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.header-left p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.exam-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.exam-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.3s;
}

.exam-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.exam-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  flex: 1;
  line-height: 1.4;
}

.exam-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.exam-description {
  font-size: 14px;
  color: #999;
  line-height: 1.4;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exam-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #999;
}

.exam-actions {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .exam-grid {
    grid-template-columns: 1fr;
  }
  
  .exam-info {
    grid-template-columns: 1fr;
  }
  
  .exam-footer {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .exam-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
