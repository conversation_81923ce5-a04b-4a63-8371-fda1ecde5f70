# 🔧 文件名显示问题修复

## 🚨 问题描述
1. **转换历史列表文件名显示乱码** - 中文文件名显示为乱码字符
2. **活动列表文件名不显示** - 活动列表中没有显示文件名信息

## 🔍 问题原因

### 1. 文件名编码问题
- **multer上传**: 文件名在上传过程中编码不正确
- **中文字符**: 中文文件名被错误编码为latin1
- **显示乱码**: 前端显示时没有正确解码

### 2. 数据结构访问错误
- **Tasks.vue**: 错误访问`task.fileUploadId.originalFilename`
- **Dashboard.vue**: 缺少文件名显示逻辑
- **关联数据**: 后端返回的关联数据结构不匹配

## ✅ 修复方案

### 1. 后端文件名编码修复

#### multer配置修复
```typescript
// 修复前
filename: (req, file, cb) => {
  const ext = path.extname(file.originalname);
  const filename = `${uuidv4()}${ext}`;
  cb(null, filename);
}

// 修复后
filename: (req, file, cb) => {
  // 确保原始文件名正确编码
  const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
  const ext = path.extname(originalName);
  const filename = `${uuidv4()}${ext}`;
  
  // 将正确编码的原始文件名存储到file对象中
  file.originalname = originalName;
  
  cb(null, filename);
}
```

#### 下载文件名编码修复
```typescript
// 确保文件名正确编码，避免中文乱码
const encodedFilename = encodeURIComponent(file.originalFilename);
res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
res.download(file.filepath, file.originalFilename);
```

### 2. 前端文件名显示修复

#### 添加文件名格式化函数
```typescript
const formatFilename = (filename: string) => {
  if (!filename) return '未知文件'
  
  try {
    // 尝试解码可能被错误编码的文件名
    if (filename.includes('%')) {
      return decodeURIComponent(filename)
    }
    
    // 检查是否是乱码（包含特殊字符）
    if (/[^\x00-\x7F]/.test(filename) && !/[\u4e00-\u9fa5]/.test(filename)) {
      // 可能是编码问题，尝试重新编码
      try {
        return decodeURIComponent(escape(filename))
      } catch {
        return filename
      }
    }
    
    return filename
  } catch (error) {
    console.warn('文件名格式化失败:', error)
    return filename || '未知文件'
  }
}
```

#### 修复数据访问错误
```typescript
// Tasks.vue - 修复前
<span v-if="task.fileUploadId">
  文件：{{ task.fileUploadId.originalFilename }}
</span>

// Tasks.vue - 修复后
<span v-if="task.fileUpload && task.fileUpload.originalFilename">
  文件：{{ formatFilename(task.fileUpload.originalFilename) }}
</span>
```

#### 添加活动标题生成
```typescript
// Dashboard.vue - 生成活动标题
const getActivityTitle = (activity: any) => {
  if (activity.title) {
    return activity.title
  }
  
  const taskTypeMap = {
    'pdf_convert': 'PDF转Word',
    'question_parse': '题目解析',
    'exam_generate': '试卷生成'
  }
  
  const taskTypeName = taskTypeMap[activity.taskType] || '文件处理'
  
  if (activity.fileUpload && activity.fileUpload.originalFilename) {
    const filename = formatFilename(activity.fileUpload.originalFilename)
    return `${taskTypeName} - ${filename}`
  }
  
  return taskTypeName
}
```

## 🔧 修复内容

### 1. 后端修复
- ✅ **multer文件名编码**: 正确处理中文文件名
- ✅ **下载响应头**: 设置正确的UTF-8编码
- ✅ **文件名存储**: 确保数据库中存储正确编码的文件名

### 2. 前端修复
- ✅ **PdfConvert.vue**: 添加文件名格式化和调试信息
- ✅ **Tasks.vue**: 修复数据访问路径和文件名显示
- ✅ **Dashboard.vue**: 添加活动标题生成逻辑
- ✅ **编码处理**: 统一的文件名编码处理函数

### 3. 调试支持
- ✅ **控制台日志**: 添加详细的调试信息
- ✅ **编码检查**: 显示文件名编码状态
- ✅ **错误处理**: 优雅处理编码失败情况

## 📊 修复前后对比

### 修复前
- ❌ 中文文件名显示为乱码
- ❌ 活动列表没有文件名
- ❌ 任务列表文件名不显示
- ❌ 下载文件名乱码

### 修复后
- ✅ 中文文件名正确显示
- ✅ 活动列表显示"任务类型 - 文件名"
- ✅ 任务列表正确显示文件名
- ✅ 下载文件名正确编码

## 🧪 测试验证

### 1. 上传中文文件名的PDF
1. 选择中文文件名的PDF文件（如：测试文档.pdf）
2. 上传并等待转换完成
3. 检查转换历史列表中的文件名显示

### 2. 检查活动列表
1. 在Dashboard页面查看最近活动
2. 验证活动标题显示格式："PDF转Word - 测试文档.pdf"

### 3. 检查任务列表
1. 在Tasks页面查看任务列表
2. 验证任务信息中的文件名正确显示

### 4. 测试文件下载
1. 点击下载按钮
2. 验证下载的文件名是否正确

## 🎯 预防措施

### 1. 文件上传最佳实践
- 始终使用UTF-8编码处理文件名
- 在multer配置中正确转换编码
- 设置正确的HTTP响应头

### 2. 前端显示最佳实践
- 统一使用文件名格式化函数
- 添加编码检测和处理逻辑
- 提供备用显示方案

### 3. 调试和监控
- 添加详细的日志记录
- 监控文件名编码问题
- 提供用户反馈机制

## 🎉 总结

通过修复文件名编码和数据访问问题：

1. ✅ **解决乱码**: 中文文件名正确显示
2. ✅ **完善显示**: 活动和任务列表显示文件名
3. ✅ **统一处理**: 所有页面使用统一的文件名格式化
4. ✅ **改善体验**: 用户可以清楚看到文件处理状态

现在所有页面的文件名都应该正确显示，不再有乱码或缺失的问题！
