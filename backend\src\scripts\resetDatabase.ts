#!/usr/bin/env ts-node

/**
 * 数据库重置脚本
 * 解决索引过多问题
 */

import dotenv from 'dotenv';
import { sequelize } from '../config/database';
import { User } from '../models/User';
import { Question } from '../models/Question';
import { FileUpload } from '../models/FileUpload';
import { ProcessingTask } from '../models/ProcessingTask';
import { Exam } from '../models/Exam';
import readline from 'readline';

// 加载环境变量
dotenv.config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

async function resetDatabase() {
  console.log('🚨 数据库重置工具');
  console.log('================================');
  console.log('⚠️  警告：此操作将删除所有数据！');
  console.log('');

  try {
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 确认操作
    const confirm1 = await askQuestion('确定要重置数据库吗？这将删除所有数据！(yes/no): ');
    if (confirm1.toLowerCase() !== 'yes') {
      console.log('❌ 操作已取消');
      rl.close();
      return;
    }

    const confirm2 = await askQuestion('再次确认：真的要删除所有数据吗？(YES/no): ');
    if (confirm2 !== 'YES') {
      console.log('❌ 操作已取消');
      rl.close();
      return;
    }

    console.log('🔄 开始重置数据库...');

    // 1. 删除所有表
    console.log('📋 删除现有表...');
    await sequelize.drop();
    console.log('✅ 所有表已删除');

    // 2. 重新创建表结构
    console.log('🏗️ 重新创建表结构...');
    await sequelize.sync({ force: true });
    console.log('✅ 表结构创建完成');

    // 3. 检查表结构
    console.log('🔍 检查表结构...');
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log('📊 创建的表:', tables);

    // 4. 检查索引
    for (const table of tables) {
      try {
        const indexes = await sequelize.getQueryInterface().showIndex(table);
        console.log(`📋 ${table} 表索引数量: ${indexes.length}`);
      } catch (error) {
        console.log(`⚠️ 无法获取 ${table} 表的索引信息`);
      }
    }

    console.log('');
    console.log('🎉 数据库重置完成！');
    console.log('💡 现在可以运行初始化脚本创建默认数据：');
    console.log('   npm run init-db');

  } catch (error) {
    console.error('❌ 数据库重置失败:', error);
  } finally {
    rl.close();
    await sequelize.close();
  }
}

async function checkDatabaseStatus() {
  console.log('🔍 检查数据库状态');
  console.log('================================');

  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 检查表
    const tables = await sequelize.getQueryInterface().showAllTables();
    console.log('📊 现有表:', tables);

    // 检查每个表的索引数量
    for (const table of tables) {
      try {
        const indexes = await sequelize.getQueryInterface().showIndex(table);
        console.log(`📋 ${table}: ${indexes.length} 个索引`);
        
        if (indexes.length > 10) {
          console.log(`⚠️  ${table} 表索引较多，可能需要清理`);
        }
      } catch (error) {
        console.log(`❌ 无法检查 ${table} 表的索引`);
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--check')) {
    await checkDatabaseStatus();
  } else if (args.includes('--reset')) {
    await resetDatabase();
  } else {
    console.log('数据库管理工具');
    console.log('');
    console.log('用法:');
    console.log('  npm run ts-node src/scripts/resetDatabase.ts -- --check   # 检查数据库状态');
    console.log('  npm run ts-node src/scripts/resetDatabase.ts -- --reset   # 重置数据库');
    console.log('');
    console.log('⚠️  重置操作将删除所有数据，请谨慎使用！');
  }
}

// 运行
main().catch(console.error);
