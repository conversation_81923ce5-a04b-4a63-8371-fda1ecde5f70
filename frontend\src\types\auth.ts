export interface User {
  id: string
  username: string
  email: string
  fullName?: string
  avatarUrl?: string
  userType: 'free' | 'premium' | 'admin'
  subscriptionExpiresAt?: string
  dailyUsage?: {
    pdfConvert: number
    questionParse: number
    examGenerate: number
  }
  lastUsageReset?: string
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  fullName?: string
}

export interface LoginResponse {
  message: string
  user: User
  token: string
}

export interface RegisterResponse {
  message: string
  user: User
  token: string
}

export interface RefreshTokenRequest {
  token: string
}

export interface RefreshTokenResponse {
  message: string
  token: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  newPassword: string
}
