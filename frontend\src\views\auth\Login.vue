<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1 class="login-title">d2x</h1>
        <p class="login-subtitle">智能试题解析平台</p>
      </div>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="email">
          <el-input
            v-model="form.email"
            type="email"
            placeholder="请输入用户名或邮箱"
            size="large"
            :prefix-icon="Message"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.loading"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <el-link type="primary" @click="$router.push('/register')">
          还没有账号？立即注册
        </el-link>
        <el-link type="info" @click="handleForgotPassword">
          忘记密码？
        </el-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Message, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'
import type { LoginRequest } from '@/types/auth'

const router = useRouter()
const authStore = useAuthStore()
const formRef = ref<FormInstance>()

const form = reactive<LoginRequest>({
  email: '',
  password: ''
})

// 自定义验证器：验证用户名或邮箱
const validateUsernameOrEmail = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入用户名或邮箱'))
    return
  }

  // 检查是否为邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  // 检查是否为有效用户名格式（3-50位字母数字）
  const usernameRegex = /^[a-zA-Z0-9]{3,50}$/

  if (emailRegex.test(value) || usernameRegex.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的用户名（3-50位字母数字）或邮箱格式'))
  }
}

const rules: FormRules = {
  email: [
    { required: true, validator: validateUsernameOrEmail, trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    await authStore.login(form)
    router.push('/dashboard')
  } catch (error: any) {
    console.error('Login failed:', error)

    // 检查是否是邮箱未验证错误
    if (error.response?.data?.code === 'EMAIL_NOT_VERIFIED') {
      ElMessageBox.confirm(
        '您的邮箱尚未验证，需要先验证邮箱才能登录。是否重新发送验证邮件？',
        '邮箱未验证',
        {
          confirmButtonText: '重新发送',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        try {
          await authApi.resendVerificationEmail(form.email)
          ElMessage.success('验证邮件已发送，请检查您的邮箱')
        } catch (resendError) {
          ElMessage.error('发送验证邮件失败，请稍后重试')
        }
      }).catch(() => {
        // 用户取消
      })
    }
  }
}

const handleForgotPassword = () => {
  ElMessage.info('忘记密码功能开发中...')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-title {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.login-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

.login-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }
  
  .login-title {
    font-size: 28px;
  }
}
</style>
