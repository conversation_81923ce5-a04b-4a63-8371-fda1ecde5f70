import nodemailer from 'nodemailer';
import { User } from '../models/User';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;

  constructor() {
    this.fromEmail = process.env.SMTP_FROM || process.env.SMTP_USER || '<EMAIL>';
    
    // 创建邮件传输器
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  // 发送邮箱验证邮件
  async sendEmailVerification(user: User, verificationToken: string): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email?token=${verificationToken}&email=${encodeURIComponent(user.email)}`;
    
    const mailOptions = {
      from: {
        name: 'd2x 智能试题解析平台',
        address: this.fromEmail
      },
      to: user.email,
      subject: '验证您的邮箱地址 - d2x',
      html: this.getEmailVerificationTemplate(user.fullName || user.username, verificationUrl),
      text: `
        您好 ${user.fullName || user.username}，

        感谢您注册 d2x 智能试题解析平台！

        请点击以下链接验证您的邮箱地址：
        ${verificationUrl}

        此链接将在24小时后失效。

        如果您没有注册此账号，请忽略此邮件。

        d2x 团队
      `
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`✅ 邮箱验证邮件已发送至: ${user.email}`);
    } catch (error) {
      console.error('❌ 发送邮箱验证邮件失败:', error);
      throw new Error('发送验证邮件失败');
    }
  }

  // 发送密码重置邮件
  async sendPasswordReset(user: User, resetToken: string): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/reset-password?token=${resetToken}&email=${encodeURIComponent(user.email)}`;
    
    const mailOptions = {
      from: {
        name: 'd2x 智能试题解析平台',
        address: this.fromEmail
      },
      to: user.email,
      subject: '重置您的密码 - d2x',
      html: this.getPasswordResetTemplate(user.fullName || user.username, resetUrl),
      text: `
        您好 ${user.fullName || user.username}，

        我们收到了重置您账号密码的请求。

        请点击以下链接重置您的密码：
        ${resetUrl}

        此链接将在1小时后失效。

        如果您没有请求重置密码，请忽略此邮件。

        d2x 团队
      `
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`✅ 密码重置邮件已发送至: ${user.email}`);
    } catch (error) {
      console.error('❌ 发送密码重置邮件失败:', error);
      throw new Error('发送重置邮件失败');
    }
  }

  // 发送欢迎邮件
  async sendWelcomeEmail(user: User): Promise<void> {
    const mailOptions = {
      from: {
        name: 'd2x 智能试题解析平台',
        address: this.fromEmail
      },
      to: user.email,
      subject: '欢迎加入 d2x！',
      html: this.getWelcomeTemplate(user.fullName || user.username),
      text: `
        您好 ${user.fullName || user.username}，

        欢迎加入 d2x 智能试题解析平台！

        您现在可以开始使用我们的服务：
        - PDF转Word文档
        - 智能试题解析
        - 题库管理
        - 智能组卷

        如有任何问题，请随时联系我们的客服团队。

        祝您使用愉快！

        d2x 团队
      `
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`✅ 欢迎邮件已发送至: ${user.email}`);
    } catch (error) {
      console.error('❌ 发送欢迎邮件失败:', error);
      // 欢迎邮件失败不应该阻止流程
    }
  }

  // 邮箱验证邮件模板
  private getEmailVerificationTemplate(userName: string, verificationUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>验证您的邮箱地址</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #409eff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>d2x</h1>
            <p>智能试题解析平台</p>
          </div>
          <div class="content">
            <h2>验证您的邮箱地址</h2>
            <p>您好 ${userName}，</p>
            <p>感谢您注册 d2x 智能试题解析平台！为了确保账号安全，请验证您的邮箱地址。</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">验证邮箱地址</a>
            </p>
            <p><strong>注意：</strong>此链接将在24小时后失效。</p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; background: #e8eaec; padding: 10px; border-radius: 4px;">${verificationUrl}</p>
            <p>如果您没有注册此账号，请忽略此邮件。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>&copy; 2024 d2x. 保留所有权利.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // 密码重置邮件模板
  private getPasswordResetTemplate(userName: string, resetUrl: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>重置您的密码</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #f56c6c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>d2x</h1>
            <p>智能试题解析平台</p>
          </div>
          <div class="content">
            <h2>重置您的密码</h2>
            <p>您好 ${userName}，</p>
            <p>我们收到了重置您账号密码的请求。</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">重置密码</a>
            </p>
            <p><strong>注意：</strong>此链接将在1小时后失效。</p>
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; background: #e8eaec; padding: 10px; border-radius: 4px;">${resetUrl}</p>
            <p>如果您没有请求重置密码，请忽略此邮件。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>&copy; 2024 d2x. 保留所有权利.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // 欢迎邮件模板
  private getWelcomeTemplate(userName: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>欢迎加入 d2x</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8fafc; padding: 30px; border-radius: 0 0 8px 8px; }
          .feature { background: white; padding: 20px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #409eff; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 欢迎加入 d2x！</h1>
            <p>智能试题解析平台</p>
          </div>
          <div class="content">
            <h2>您好 ${userName}，</h2>
            <p>欢迎加入 d2x 智能试题解析平台！您现在可以开始使用我们的强大功能：</p>
            
            <div class="feature">
              <h3>📄 PDF转Word</h3>
              <p>高精度文档转换，支持复杂公式和图表识别</p>
            </div>
            
            <div class="feature">
              <h3>🧠 智能试题解析</h3>
              <p>自动识别题号、题干、选项和答案，准确率高达99.9%</p>
            </div>
            
            <div class="feature">
              <h3>📚 题库管理</h3>
              <p>强大的题库管理系统，支持分类、搜索、标签等功能</p>
            </div>
            
            <div class="feature">
              <h3>📝 智能组卷</h3>
              <p>从题库中快速选择题目，生成个性化试卷</p>
            </div>
            
            <p>如有任何问题，请随时联系我们的客服团队。</p>
            <p>祝您使用愉快！</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>&copy; 2024 d2x. 保留所有权利.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // 测试邮件配置
  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('✅ 邮件服务配置正确');
      return true;
    } catch (error) {
      console.error('❌ 邮件服务配置错误:', error);
      return false;
    }
  }
}

// 导出单例实例
export const emailService = new EmailService();
