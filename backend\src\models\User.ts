import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import crypto from 'crypto';

// 用户类型
export type UserType = 'free' | 'premium' | 'admin';

// 每日使用量接口
export interface DailyUsage {
  pdfConvert: number;
  questionParse: number;
  examGenerate: number;
}

// 用户属性接口
export interface UserAttributes {
  id: number;
  username: string;
  email: string;
  passwordHash: string;
  fullName?: string;
  avatarUrl?: string;
  userType: UserType;
  isActive: boolean;
  isEmailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  subscriptionExpiresAt?: Date;
  dailyUsage: DailyUsage;
  lastUsageReset: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 创建用户时的可选属性
export interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// User模型类
class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number;
  public username!: string;
  public email!: string;
  public passwordHash!: string;
  public fullName?: string;
  public avatarUrl?: string;
  public userType!: UserType;
  public isActive!: boolean;
  public isEmailVerified!: boolean;
  public emailVerificationToken?: string;
  public emailVerificationExpires?: Date;
  public subscriptionExpiresAt?: Date;
  public dailyUsage!: DailyUsage;
  public lastUsageReset!: Date;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 检查是否为高级用户
  public isPremium(): boolean {
    if (this.userType === 'admin') return true;
    if (this.userType === 'premium') {
      return !this.subscriptionExpiresAt || this.subscriptionExpiresAt > new Date();
    }
    return false;
  }

  // 检查功能使用权限
  public canUseFeature(feature: keyof DailyUsage): boolean {
    if (this.isPremium()) return true;

    // 检查是否需要重置每日使用量
    this.resetDailyUsageIfNeeded();

    const limits = {
      pdfConvert: 1,
      questionParse: 1,
      examGenerate: 1
    };

    return this.dailyUsage[feature] < limits[feature];
  }

  // 增加功能使用次数
  public incrementUsage(feature: keyof DailyUsage): void {
    this.resetDailyUsageIfNeeded();
    this.dailyUsage[feature]++;
    this.save();
  }

  // 重置每日使用量（如果需要）
  private resetDailyUsageIfNeeded(): void {
    const now = new Date();
    const lastReset = new Date(this.lastUsageReset);
    
    // 如果不是同一天，重置使用量
    if (now.toDateString() !== lastReset.toDateString()) {
      this.dailyUsage = {
        pdfConvert: 0,
        questionParse: 0,
        examGenerate: 0
      };
      this.lastUsageReset = now;
    }
  }

  // 获取功能使用情况
  public getFeatureUsage(feature: keyof DailyUsage): { used: number; limit: number } {
    this.resetDailyUsageIfNeeded();

    if (this.isPremium()) {
      return { used: this.dailyUsage[feature], limit: -1 }; // -1 表示无限制
    }

    const limits = {
      pdfConvert: 1,
      questionParse: 1,
      examGenerate: 1
    };

    return {
      used: this.dailyUsage[feature],
      limit: limits[feature]
    };
  }

  // 生成邮箱验证令牌
  public generateEmailVerificationToken(): string {
    const token = crypto.randomBytes(32).toString('hex');
    this.emailVerificationToken = token;
    this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期
    return token;
  }

  // 验证邮箱令牌
  public verifyEmailToken(token: string): boolean {
    if (!this.emailVerificationToken || !this.emailVerificationExpires) {
      return false;
    }

    if (this.emailVerificationExpires < new Date()) {
      return false; // 令牌已过期
    }

    return this.emailVerificationToken === token;
  }

  // 标记邮箱已验证
  public async markEmailAsVerified(): Promise<void> {
    this.isEmailVerified = true;
    this.emailVerificationToken = undefined;
    this.emailVerificationExpires = undefined;
    await this.save();
  }

  // 检查是否需要邮箱验证
  public needsEmailVerification(): boolean {
    return !this.isEmailVerified;
  }
}

// 定义模型
User.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    validate: {
      len: [3, 50],
      isAlphanumeric: true
    }
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  passwordHash: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  fullName: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  avatarUrl: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  userType: {
    type: DataTypes.ENUM('free', 'premium', 'admin'),
    allowNull: false,
    defaultValue: 'free'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  isEmailVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  emailVerificationToken: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  emailVerificationExpires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  subscriptionExpiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  dailyUsage: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {
      pdfConvert: 0,
      questionParse: 0,
      examGenerate: 0
    }
  },
  lastUsageReset: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'User',
  tableName: 'users',
  indexes: [
    {
      unique: true,
      fields: ['username']
    },
    {
      unique: true,
      fields: ['email']
    },
    {
      fields: ['userType']
    },
    {
      fields: ['isActive']
    }
  ]
});

export { User };
export default User;
