#!/usr/bin/env ts-node

/**
 * 修复路由绑定问题的脚本
 * 为所有控制器方法添加.bind(controller)
 */

import fs from 'fs';
import path from 'path';

const routesDir = path.join(__dirname, '../routes');

function fixRouteFile(filePath: string): void {
  console.log(`修复文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 查找控制器实例名
  const controllerMatch = content.match(/const (\w+Controller) = new/);
  if (!controllerMatch) {
    console.log(`  跳过: 未找到控制器实例`);
    return;
  }
  
  const controllerName = controllerMatch[1];
  console.log(`  控制器名: ${controllerName}`);
  
  // 替换所有路由绑定
  const routePattern = new RegExp(`(router\\.[a-z]+\\([^,]+,\\s*(?:authenticateToken,\\s*)?(?:requireAdmin,\\s*)?)(${controllerName}\\.[a-zA-Z]+)(?!\\.bind)`, 'g');
  
  let matchCount = 0;
  content = content.replace(routePattern, (match, prefix, method) => {
    matchCount++;
    return `${prefix}${method}.bind(${controllerName})`;
  });
  
  if (matchCount > 0) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ 修复了 ${matchCount} 个路由绑定`);
  } else {
    console.log(`  ✅ 无需修复`);
  }
}

function main(): void {
  console.log('🔧 开始修复路由绑定问题...');
  
  const files = fs.readdirSync(routesDir);
  
  for (const file of files) {
    if (file.endsWith('.ts')) {
      const filePath = path.join(routesDir, file);
      fixRouteFile(filePath);
    }
  }
  
  console.log('✅ 路由绑定修复完成！');
}

// 运行修复
main();
