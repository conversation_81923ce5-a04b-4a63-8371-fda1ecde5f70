#!/usr/bin/env ts-node

/**
 * 文件上传功能测试脚本
 */

import dotenv from 'dotenv';
import { connectDatabase } from '../config/database';
import { FileUpload } from '../models/FileUpload';
import { ProcessingTask } from '../models/ProcessingTask';
import { DocumentProcessor } from '../services/DocumentProcessor';

// 加载环境变量
dotenv.config();

async function testFileUpload() {
  console.log('🧪 测试文件上传功能...');
  
  try {
    // 连接数据库
    await connectDatabase();
    
    // 检查FileUpload模型
    console.log('📋 检查FileUpload模型...');
    const fileUploadCount = await FileUpload.count();
    console.log(`当前文件上传记录数: ${fileUploadCount}`);
    
    // 检查最近的文件上传记录
    const recentFiles = await FileUpload.findAll({
      limit: 5,
      order: [['createdAt', 'DESC']]
    });
    
    console.log('📁 最近的文件上传记录:');
    recentFiles.forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.originalFilename}`);
      console.log(`     文件路径: ${file.filepath}`);
      console.log(`     状态: ${file.status}`);
      console.log(`     上传类型: ${file.uploadType}`);
      console.log('');
    });
    
    // 检查ProcessingTask模型
    console.log('📋 检查ProcessingTask模型...');
    const taskCount = await ProcessingTask.count();
    console.log(`当前处理任务数: ${taskCount}`);
    
    // 检查最近的处理任务
    const recentTasks = await ProcessingTask.findAll({
      limit: 5,
      order: [['createdAt', 'DESC']]
    });
    
    console.log('⚙️ 最近的处理任务:');
    recentTasks.forEach((task, index) => {
      console.log(`  ${index + 1}. 任务ID: ${task.id}`);
      console.log(`     类型: ${task.taskType}`);
      console.log(`     状态: ${task.status}`);
      console.log(`     进度: ${task.progress}%`);
      console.log('');
    });
    
    // 测试DocumentProcessor
    console.log('🔧 测试DocumentProcessor...');
    const processor = new DocumentProcessor();
    console.log('✅ DocumentProcessor实例化成功');
    
    console.log('✅ 文件上传功能测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testFileUpload().catch(console.error);
