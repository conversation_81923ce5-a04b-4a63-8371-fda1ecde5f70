import { Response } from 'express';
import fs from 'fs';
import path from 'path';
import { Op } from 'sequelize';
import { FileUpload } from '../models/FileUpload';
import { ProcessingTask } from '../models/ProcessingTask';
import { AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import { DocumentProcessor } from '../services/DocumentProcessor';

export class FileController {
  private documentProcessor = new DocumentProcessor();
  
  // PDF转Word上传
  async uploadForPdfConvert(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const file = req.file;
      
      if (!file) {
        res.status(400).json({ error: '请选择要上传的文件' });
        return;
      }
      
      // 验证文件类型
      if (file.mimetype !== 'application/pdf') {
        res.status(400).json({ error: '仅支持PDF文件' });
        return;
      }
      
      // 创建文件上传记录
      const fileUpload = await FileUpload.create({
        userId: user.id,
        originalFilename: file.originalname,
        filename: file.filename,
        filepath: file.path,
        fileSize: file.size,
        mimetype: file.mimetype,
        uploadType: 'pdf_convert',
        status: 'uploaded'
      });

      // 创建处理任务
      const task = await ProcessingTask.create({
        userId: user.id,
        fileUploadId: fileUpload.id,
        taskType: 'pdf_to_word',
        status: 'pending',
        progress: 0
      });
      
      // 增加用户使用次数
      user.incrementUsage('pdfConvert');
      await user.save();
      
      // 异步处理文档转换
      this.documentProcessor.processPdfToWord(task.id.toString())
        .catch(error => {
          console.error('PDF转换处理失败:', error);
        });

      res.status(201).json({
        message: '文件上传成功，正在处理中',
        fileId: fileUpload.id,
        taskId: task.id,
        originalFilename: file.originalname,
        fileSize: file.size
      });
    } catch (error) {
      console.error('PDF转换上传错误:', error);
      res.status(500).json({ error: '文件上传失败' });
    }
  }
  
  // 试题解析上传
  async uploadForQuestionParse(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const file = req.file;
      
      if (!file) {
        res.status(400).json({ error: '请选择要上传的文件' });
        return;
      }
      
      // 验证文件类型
      const allowedMimes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword'
      ];
      
      if (!allowedMimes.includes(file.mimetype)) {
        res.status(400).json({ error: '仅支持PDF和Word文档' });
        return;
      }
      
      // 创建文件上传记录
      const fileUpload = await FileUpload.create({
        userId: user.id,
        originalFilename: file.originalname,
        filename: file.filename,
        filepath: file.path,
        fileSize: file.size,
        mimetype: file.mimetype,
        uploadType: 'question_parse',
        status: 'uploaded'
      });

      // 创建处理任务
      const task = await ProcessingTask.create({
        userId: user.id,
        fileUploadId: fileUpload.id,
        taskType: 'question_parsing',
        status: 'pending',
        progress: 0
      });
      
      // 增加用户使用次数
      user.incrementUsage('questionParse');
      await user.save();
      
      // 异步处理试题解析
      this.documentProcessor.processQuestionParsing(task.id.toString())
        .catch(error => {
          console.error('试题解析处理失败:', error);
        });

      res.status(201).json({
        message: '文件上传成功，正在解析中',
        fileId: fileUpload.id,
        taskId: task.id,
        originalFilename: file.originalname,
        fileSize: file.size
      });
    } catch (error) {
      console.error('试题解析上传错误:', error);
      res.status(500).json({ error: '文件上传失败' });
    }
  }
  
  // 批量上传
  async batchUpload(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        res.status(400).json({ error: '请选择要上传的文件' });
        return;
      }
      
      const uploadResults = [];
      
      for (const file of files) {
        const fileUpload = await FileUpload.create({
          userId: user.id,
          originalFilename: file.originalname,
          filename: file.filename,
          filepath: file.path,
          fileSize: file.size,
          mimetype: file.mimetype,
          uploadType: file.mimetype === 'application/pdf' ? 'pdf_convert' : 'question_parse',
          status: 'uploaded'
        });

        uploadResults.push({
          fileId: fileUpload.id,
          originalFilename: file.originalname,
          fileSize: file.size,
          mimeType: file.mimetype
        });
      }
      
      res.status(201).json({
        message: `成功上传${files.length}个文件`,
        files: uploadResults
      });
    } catch (error) {
      console.error('批量上传错误:', error);
      res.status(500).json({ error: '批量上传失败' });
    }
  }
  
  // 获取用户上传的文件列表
  async getUserUploads(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { page = 1, limit = 20, uploadType, status } = req.query;
      
      const whereConditions: any = { userId: user.id };

      if (uploadType) {
        whereConditions.uploadType = uploadType;
      }

      if (status) {
        whereConditions.status = status;
      }

      const skip = (Number(page) - 1) * Number(limit);

      const [files, total] = await Promise.all([
        FileUpload.findAll({
          where: whereConditions,
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit)
        }),
        FileUpload.count({ where: whereConditions })
      ]);
      
      res.json({
        files,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('获取文件列表错误:', error);
      res.status(500).json({ error: '获取文件列表失败' });
    }
  }
  
  // 获取文件详情
  async getFileDetails(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { fileId } = req.params;
      
      const file = await FileUpload.findOne({
        where: {
          id: fileId,
          userId: user.id
        }
      });

      if (!file) {
        res.status(404).json({ error: '文件不存在' });
        return;
      }

      // 获取相关的处理任务
      const task = await ProcessingTask.findOne({
        where: {
          fileUploadId: file.id
        }
      });
      
      res.json({
        file,
        task
      });
    } catch (error) {
      console.error('获取文件详情错误:', error);
      res.status(500).json({ error: '获取文件详情失败' });
    }
  }
  
  // 下载文件
  async downloadFile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { fileId } = req.params;
      
      const file = await FileUpload.findOne({
        where: {
          id: fileId,
          userId: user.id
        }
      });

      if (!file) {
        res.status(404).json({ error: '文件不存在' });
        return;
      }

      // 检查文件是否存在
      if (!fs.existsSync(file.filepath)) {
        res.status(404).json({ error: '文件已被删除' });
        return;
      }

      // 确保文件名正确编码，避免中文乱码
      const encodedFilename = encodeURIComponent(file.originalFilename);
      res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
      res.download(file.filepath, file.originalFilename);
    } catch (error) {
      console.error('下载文件错误:', error);
      res.status(500).json({ error: '下载文件失败' });
    }
  }
  
  // 删除文件
  async deleteFile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { fileId } = req.params;
      
      const file = await FileUpload.findOne({
        where: {
          id: fileId,
          userId: user.id
        }
      });

      if (!file) {
        res.status(404).json({ error: '文件不存在' });
        return;
      }

      // 删除物理文件
      if (fs.existsSync(file.filepath)) {
        fs.unlinkSync(file.filepath);
      }

      // 删除数据库记录
      await file.destroy();

      // 删除相关的处理任务
      await ProcessingTask.destroy({
        where: { fileUploadId: fileId }
      });
      
      res.json({ message: '文件删除成功' });
    } catch (error) {
      console.error('删除文件错误:', error);
      res.status(500).json({ error: '删除文件失败' });
    }
  }

  // 富文本编辑器图片上传
  async uploadImage(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const file = req.file;

      if (!file) {
        res.status(400).json({ error: '请选择要上传的图片' });
        return;
      }

      // 验证图片类型
      const allowedMimes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp'
      ];

      if (!allowedMimes.includes(file.mimetype)) {
        res.status(400).json({ error: '不支持的图片格式，仅支持 JPG、PNG、GIF、WebP' });
        return;
      }

      // 验证文件大小 (5MB)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        res.status(400).json({ error: '图片大小不能超过 5MB' });
        return;
      }

      console.log('📸 处理图片上传:', {
        originalname: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        filename: file.filename
      });

      // 保存文件记录到数据库
      const fileUpload = await FileUpload.create({
        userId: user.id,
        originalFilename: file.originalname,
        filename: file.filename,
        filepath: file.path,
        mimetype: file.mimetype,
        fileSize: file.size,
        uploadType: 'image',
        status: 'completed'
      });

      // 生成访问URL
      const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const imageUrl = `${baseUrl}/uploads/${file.filename}`;

      res.json({
        message: '图片上传成功',
        fileId: fileUpload.id,
        url: imageUrl,
        originalName: file.originalname,
        size: file.size
      });

    } catch (error) {
      console.error('Image upload error:', error);
      res.status(500).json({ error: '图片上传失败' });
    }
  }
}
