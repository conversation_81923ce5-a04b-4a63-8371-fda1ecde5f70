#!/usr/bin/env node

/**
 * 邮件配置检查脚本
 * 用于验证 .env 文件中的邮件配置是否正确
 */

const fs = require('fs');
const path = require('path');

console.log('📧 d2x 邮件配置检查工具');
console.log('================================');

// 检查 .env 文件是否存在
const envPath = path.join(__dirname, 'backend', '.env');
if (!fs.existsSync(envPath)) {
  console.log('❌ 未找到 backend/.env 文件');
  console.log('请先运行启动脚本创建配置文件：');
  console.log('  Linux/macOS: ./start.sh');
  console.log('  Windows: start.bat');
  process.exit(1);
}

// 读取 .env 文件
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

// 解析环境变量
envContent.split('\n').forEach(line => {
  const trimmed = line.trim();
  if (trimmed && !trimmed.startsWith('#')) {
    const [key, ...valueParts] = trimmed.split('=');
    if (key && valueParts.length > 0) {
      envVars[key.trim()] = valueParts.join('=').trim();
    }
  }
});

console.log('📋 检查邮件配置...\n');

// 必需的邮件配置项
const requiredEmailVars = [
  'SMTP_HOST',
  'SMTP_PORT',
  'SMTP_USER',
  'SMTP_PASS'
];

// 可选的邮件配置项
const optionalEmailVars = [
  'SMTP_SECURE',
  'SMTP_FROM',
  'FRONTEND_URL'
];

let hasErrors = false;
let hasWarnings = false;

// 检查必需配置
console.log('🔍 必需配置项:');
requiredEmailVars.forEach(varName => {
  const value = envVars[varName];
  if (!value || value === '<EMAIL>' || value === 'your-app-password') {
    console.log(`❌ ${varName}: 未配置或使用默认值`);
    hasErrors = true;
  } else {
    console.log(`✅ ${varName}: 已配置`);
  }
});

// 检查可选配置
console.log('\n🔍 可选配置项:');
optionalEmailVars.forEach(varName => {
  const value = envVars[varName];
  if (!value) {
    console.log(`⚠️  ${varName}: 未配置（将使用默认值）`);
    hasWarnings = true;
  } else {
    console.log(`✅ ${varName}: ${value}`);
  }
});

// 特殊检查
console.log('\n🔍 配置验证:');

// 检查端口号
const smtpPort = envVars['SMTP_PORT'];
if (smtpPort && !['25', '465', '587', '2525'].includes(smtpPort)) {
  console.log(`⚠️  SMTP_PORT: ${smtpPort} 不是常用端口，请确认是否正确`);
  hasWarnings = true;
}

// 检查Gmail配置
const smtpHost = envVars['SMTP_HOST'];
const smtpUser = envVars['SMTP_USER'];
if (smtpHost === 'smtp.gmail.com' && smtpUser && smtpUser.includes('@gmail.com')) {
  const smtpPass = envVars['SMTP_PASS'];
  if (smtpPass && smtpPass.length < 16) {
    console.log('⚠️  Gmail用户建议使用应用专用密码（16位）而不是普通密码');
    hasWarnings = true;
  }
}

// 检查前端URL
const frontendUrl = envVars['FRONTEND_URL'];
if (!frontendUrl) {
  console.log('⚠️  FRONTEND_URL 未配置，邮件中的链接可能无法正常工作');
  hasWarnings = true;
} else if (!frontendUrl.startsWith('http')) {
  console.log('⚠️  FRONTEND_URL 应该包含协议（http:// 或 https://）');
  hasWarnings = true;
}

// 输出结果
console.log('\n📊 检查结果:');
if (hasErrors) {
  console.log('❌ 发现配置错误，邮件功能可能无法正常工作');
  console.log('\n💡 解决方案:');
  console.log('1. 编辑 backend/.env 文件');
  console.log('2. 配置正确的SMTP服务器信息');
  console.log('3. 参考 docs/EMAIL_SETUP.md 获取详细配置说明');
  console.log('4. 运行测试命令: cd backend && npm run test-email');
} else if (hasWarnings) {
  console.log('⚠️  配置基本正确，但有一些建议优化的地方');
  console.log('可以运行测试命令验证: cd backend && npm run test-email');
} else {
  console.log('✅ 邮件配置看起来正确！');
  console.log('建议运行测试命令验证: cd backend && npm run test-email');
}

// 提供常用邮件服务商配置示例
if (hasErrors) {
  console.log('\n📧 常用邮件服务商配置示例:');
  console.log('\n1. Gmail:');
  console.log('   SMTP_HOST=smtp.gmail.com');
  console.log('   SMTP_PORT=587');
  console.log('   SMTP_SECURE=false');
  console.log('   SMTP_USER=<EMAIL>');
  console.log('   SMTP_PASS=your-16-digit-app-password');
  
  console.log('\n2. QQ邮箱:');
  console.log('   SMTP_HOST=smtp.qq.com');
  console.log('   SMTP_PORT=587');
  console.log('   SMTP_SECURE=false');
  console.log('   SMTP_USER=<EMAIL>');
  console.log('   SMTP_PASS=your-authorization-code');
  
  console.log('\n3. 163邮箱:');
  console.log('   SMTP_HOST=smtp.163.com');
  console.log('   SMTP_PORT=587');
  console.log('   SMTP_SECURE=false');
  console.log('   SMTP_USER=<EMAIL>');
  console.log('   SMTP_PASS=your-authorization-code');
}

console.log('\n📚 更多帮助: docs/EMAIL_SETUP.md');

process.exit(hasErrors ? 1 : 0);
