#!/usr/bin/env node

/**
 * 简单的启动测试脚本
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 测试后端启动...');

// 启动后端
const backend = spawn('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'backend'),
  stdio: 'inherit',
  shell: true
});

backend.on('error', (error) => {
  console.error('❌ 后端启动失败:', error);
});

backend.on('close', (code) => {
  console.log(`后端进程退出，代码: ${code}`);
});

// 5秒后检查
setTimeout(() => {
  console.log('⏰ 5秒测试完成，停止进程...');
  backend.kill();
}, 5000);
