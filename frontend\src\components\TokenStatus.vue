<template>
  <div class="token-status" v-if="authStore.isAuthenticated">
    <el-tooltip :content="tooltipContent" placement="bottom">
      <el-badge 
        :value="badgeValue" 
        :type="badgeType"
        :hidden="!showBadge"
      >
        <el-button 
          :type="buttonType" 
          size="small" 
          circle
          :icon="statusIcon"
          @click="handleClick"
          :loading="tokenRefresh.isRefreshing.value"
        />
      </el-badge>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useGlobalTokenRefresh } from '@/composables/useTokenRefresh'
import { ElMessage } from 'element-plus'
import { 
  Clock, 
  Refresh, 
  Warning,
  Check
} from '@element-plus/icons-vue'

const authStore = useAuthStore()
const tokenRefresh = useGlobalTokenRefresh()
const tokenStatus = ref(tokenRefresh.getTokenStatus())

// 定时更新状态
let statusTimer: number | null = null

const updateStatus = () => {
  tokenStatus.value = tokenRefresh.getTokenStatus()
}

// 计算属性
const tooltipContent = computed(() => {
  if (tokenStatus.value.isExpired) {
    return 'Token已过期'
  }
  if (tokenStatus.value.shouldRefresh) {
    return `Token即将过期，剩余时间: ${tokenStatus.value.remainingTime}`
  }
  return `Token状态正常，剩余时间: ${tokenStatus.value.remainingTime}`
})

const badgeValue = computed(() => {
  if (tokenStatus.value.isExpired) return '!'
  if (tokenStatus.value.shouldRefresh) return '⚠'
  return ''
})

const badgeType = computed(() => {
  if (tokenStatus.value.isExpired) return 'danger'
  if (tokenStatus.value.shouldRefresh) return 'warning'
  return 'success'
})

const showBadge = computed(() => {
  return tokenStatus.value.isExpired || tokenStatus.value.shouldRefresh
})

const buttonType = computed(() => {
  if (tokenStatus.value.isExpired) return 'danger'
  if (tokenStatus.value.shouldRefresh) return 'warning'
  return 'success'
})

const statusIcon = computed(() => {
  if (tokenRefresh.isRefreshing.value) return Refresh
  if (tokenStatus.value.isExpired) return Warning
  if (tokenStatus.value.shouldRefresh) return Clock
  return Check
})

// 点击处理
const handleClick = async () => {
  if (tokenStatus.value.isExpired) {
    ElMessage.error('Token已过期，请重新登录')
    authStore.logout()
    return
  }
  
  if (tokenStatus.value.shouldRefresh) {
    await tokenRefresh.manualRefreshToken()
    updateStatus()
  } else {
    ElMessage.info(`Token状态正常，剩余时间: ${tokenStatus.value.remainingTime}`)
  }
}

onMounted(() => {
  // 每10秒更新一次状态显示
  statusTimer = window.setInterval(updateStatus, 10 * 1000)
})

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})
</script>

<style scoped>
.token-status {
  display: inline-block;
}

.el-button.is-circle {
  width: 28px;
  height: 28px;
  padding: 0;
}
</style>
