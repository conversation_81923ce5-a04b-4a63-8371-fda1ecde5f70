# 🔧 Redis配置指南

## 📋 概述

d2x项目支持可选的Redis缓存。您可以通过环境变量控制是否启用Redis。

## ⚙️ 配置选项

### 禁用Redis（默认）
```bash
# .env文件中设置
ENABLE_REDIS=false
```

### 启用Redis
```bash
# .env文件中设置
ENABLE_REDIS=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
```

## 🚀 使用方式

### 1. 不使用Redis（当前配置）
- 应用程序将使用内存缓存作为备用
- 无需安装或配置Redis服务器
- 适合开发环境和小型部署

### 2. 启用Redis缓存
- 提供更好的缓存性能
- 支持分布式缓存
- 适合生产环境

## 📊 缓存功能

无论是否启用Redis，应用程序都提供完整的缓存功能：

```typescript
import { CacheService } from '../utils/cache';

// 设置缓存
await CacheService.set('user:123', userData, 3600);

// 获取缓存
const userData = await CacheService.get('user:123');

// 删除缓存
await CacheService.del('user:123');

// 检查缓存是否存在
const exists = await CacheService.exists('user:123');
```

## 🔄 切换Redis状态

### 禁用Redis
1. 修改`.env`文件：`ENABLE_REDIS=false`
2. 重启应用程序
3. 应用程序将自动切换到内存缓存

### 启用Redis
1. 安装并启动Redis服务器
2. 修改`.env`文件：`ENABLE_REDIS=true`
3. 配置Redis连接参数
4. 重启应用程序

## 📈 性能对比

| 特性 | 内存缓存 | Redis缓存 |
|------|----------|-----------|
| 性能 | 极快 | 快 |
| 持久化 | 否 | 可选 |
| 分布式 | 否 | 是 |
| 内存使用 | 应用程序内存 | 独立进程 |
| 适用场景 | 开发/小型部署 | 生产/大型部署 |

## 🛠️ Redis安装（可选）

### Windows
```bash
# 使用Chocolatey
choco install redis-64

# 或下载Windows版本
# https://github.com/microsoftarchive/redis/releases
```

### Linux/Mac
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# macOS
brew install redis
```

### Docker
```bash
docker run -d --name redis -p 6379:6379 redis:alpine
```

## 🔍 监控和调试

### 检查缓存状态
```typescript
const cacheInfo = CacheService.getCacheInfo();
console.log(`缓存类型: ${cacheInfo.type}, 大小: ${cacheInfo.size}`);
```

### 日志输出
应用程序启动时会显示Redis状态：
- `✅ Redis connected successfully` - Redis已启用并连接
- `📝 Redis已禁用，应用程序将在无缓存模式下运行` - Redis已禁用

## 🎯 推荐配置

### 开发环境
```bash
ENABLE_REDIS=false
```

### 生产环境
```bash
ENABLE_REDIS=true
REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=secure_password
```

## 🔒 安全建议

1. **生产环境**：始终设置Redis密码
2. **网络安全**：限制Redis访问IP
3. **防火墙**：只开放必要的端口
4. **监控**：监控Redis性能和内存使用

---

**注意**: 当前配置为`ENABLE_REDIS=false`，应用程序将正常运行而无需Redis服务器。
