<template>
  <Layout>
    <div class="question-bank">
      <div class="page-header">
        <div class="header-left">
          <h2>题库管理</h2>
          <p>管理您的题目资源，支持搜索、筛选和编辑</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            添加题目
          </el-button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="filter-section">
        <div class="card">
          <div class="filter-content">
            <div class="filter-row">
              <div class="filter-item">
                <el-input
                  v-model="filters.keyword"
                  placeholder="搜索题目内容..."
                  clearable
                  @keyup.enter="loadQuestions"
                  @clear="loadQuestions"
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
              
              <div class="filter-item">
                <el-select
                  v-model="filters.questionType"
                  placeholder="题目类型"
                  clearable
                  @change="loadQuestions"
                >
                  <el-option label="单选题" value="single_choice" />
                  <el-option label="多选题" value="multiple_choice" />
                  <el-option label="判断题" value="true_false" />
                  <el-option label="填空题" value="fill_blank" />
                  <el-option label="简答题" value="short_answer" />
                  <el-option label="论述题" value="essay" />
                  <el-option label="计算题" value="calculation" />
                </el-select>
              </div>
              
              <div class="filter-item">
                <el-select
                  v-model="filters.subject"
                  placeholder="学科"
                  clearable
                  @change="loadQuestions"
                >
                  <el-option
                    v-for="subject in subjects"
                    :key="subject"
                    :label="subject"
                    :value="subject"
                  />
                </el-select>
              </div>
              
              <div class="filter-item">
                <el-select
                  v-model="filters.difficultyLevel"
                  placeholder="难度"
                  clearable
                  @change="loadQuestions"
                >
                  <el-option label="很简单" :value="1" />
                  <el-option label="简单" :value="2" />
                  <el-option label="中等" :value="3" />
                  <el-option label="困难" :value="4" />
                  <el-option label="很困难" :value="5" />
                </el-select>
              </div>
              
              <div class="filter-item">
                <el-button type="primary" @click="loadQuestions">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetFilters">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 题目列表 -->
      <div class="questions-section">
        <div class="card">
          <div class="card-header">
            <div class="header-info">
              <h3>题目列表</h3>
              <span class="count-info">共 {{ pagination.total }} 道题目</span>
            </div>
            <div class="header-actions">
              <el-button @click="toggleSelectAll">
                {{ selectedQuestions.length > 0 ? '取消全选' : '全选' }}
              </el-button>
              <el-button
                v-if="selectedQuestions.length > 0"
                type="danger"
                @click="batchDelete"
              >
                <el-icon><Delete /></el-icon>
                批量删除 ({{ selectedQuestions.length }})
              </el-button>
            </div>
          </div>
          
          <div class="questions-list" v-loading="loading">
            <div
              v-for="question in questionList"
              :key="question.id"
              class="question-card"
              :class="{ selected: selectedQuestions.includes(question.id) }"
              @click="toggleSelect(question.id)"
            >
              <div class="question-header">
                <div class="question-info">
                  <span class="question-number">{{ question.questionNumber || '未编号' }}</span>
                  <el-tag :type="getQuestionTypeColor(question.questionType)" size="small">
                    {{ getQuestionTypeText(question.questionType) }}
                  </el-tag>
                  <el-tag v-if="question.subject" type="info" size="small">
                    {{ question.subject }}
                  </el-tag>
                  <div class="difficulty-stars">
                    <el-rate
                      v-model="question.difficultyLevel"
                      disabled
                      show-score
                      text-color="#ff9900"
                      score-template="难度 {value}"
                    />
                  </div>
                </div>
                <div class="question-actions">
                  <el-button size="small" @click.stop="editQuestion(question)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button size="small" @click.stop="addToExam(question)">
                    <el-icon><Plus /></el-icon>
                    加入试卷
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click.stop="deleteQuestion(question.id)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </div>
              
              <div class="question-stem">{{ question.stem }}</div>
              
              <div v-if="question.options && question.options.length > 0" class="question-options">
                <div
                  v-for="option in question.options"
                  :key="option.key"
                  class="option-item"
                  :class="{ correct: option.isCorrect }"
                >
                  <span class="option-key">{{ option.key }}.</span>
                  <span class="option-content">{{ option.content }}</span>
                </div>
              </div>
              
              <div class="question-footer">
                <div class="question-tags">
                  <el-tag
                    v-for="tag in question.tags"
                    :key="tag"
                    size="small"
                    class="question-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
                <div class="question-meta">
                  <span>创建时间: {{ formatTime(question.createdAt) }}</span>
                </div>
              </div>
            </div>
            
            <el-empty v-if="!loading && questionList.length === 0" description="暂无题目数据" />
          </div>
          
          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="pagination.total > 0">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadQuestions"
              @current-change="loadQuestions"
            />
          </div>
        </div>
      </div>

      <!-- 创建/编辑题目对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="editingQuestion ? '编辑题目' : '创建题目'"
        width="70%"
        :before-close="closeDialog"
      >
        <QuestionForm
          :question="editingQuestion"
          @save="handleSave"
          @cancel="closeDialog"
        />
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Plus,
  Search,
  Refresh,
  Delete,
  Edit
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import QuestionForm from '@/components/QuestionForm.vue'
import { http } from '@/utils/http'

const router = useRouter()

const loading = ref(false)
const dialogVisible = ref(false)
const questionList = ref([])
const selectedQuestions = ref([])
const editingQuestion = ref(null)
const subjects = ref([])

const filters = reactive({
  keyword: '',
  questionType: '',
  subject: '',
  difficultyLevel: null
})

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0
})

const loadQuestions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
      ...filters
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })
    
    const response = await http.get('/question', { params })
    questionList.value = response.questions || []
    pagination.value.total = response.pagination?.total || 0
    
    // 提取学科列表
    const subjectSet = new Set()
    questionList.value.forEach(q => {
      if (q.subject) subjectSet.add(q.subject)
    })
    subjects.value = Array.from(subjectSet)
    
  } catch (error) {
    console.error('Load questions error:', error)
    ElMessage.error('加载题目失败')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = key === 'difficultyLevel' ? null : ''
  })
  pagination.value.page = 1
  loadQuestions()
}

const toggleSelect = (questionId: string) => {
  const index = selectedQuestions.value.indexOf(questionId)
  if (index > -1) {
    selectedQuestions.value.splice(index, 1)
  } else {
    selectedQuestions.value.push(questionId)
  }
}

const toggleSelectAll = () => {
  if (selectedQuestions.value.length > 0) {
    selectedQuestions.value = []
  } else {
    selectedQuestions.value = questionList.value.map(q => q.id)
  }
}

const showCreateDialog = () => {
  router.push('/question/add')
}

const editQuestion = (question: any) => {
  router.push(`/question/edit/${question.id}`)
}

const closeDialog = () => {
  dialogVisible.value = false
  editingQuestion.value = null
}

const handleSave = async (questionData: any) => {
  try {
    if (editingQuestion.value) {
      await http.put(`/question/${editingQuestion.value.id}`, questionData)
      ElMessage.success('题目更新成功')
    } else {
      await http.post('/question', questionData)
      ElMessage.success('题目创建成功')
    }
    
    closeDialog()
    loadQuestions()
  } catch (error) {
    console.error('Save question error:', error)
    ElMessage.error('保存题目失败')
  }
}

const deleteQuestion = async (questionId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这道题目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await http.delete(`/question/${questionId}`)
    ElMessage.success('题目删除成功')
    loadQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete question error:', error)
      ElMessage.error('删除题目失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedQuestions.value.length} 道题目吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await http.delete('/question/batch', {
      data: { questionIds: selectedQuestions.value }
    })
    
    ElMessage.success('批量删除成功')
    selectedQuestions.value = []
    loadQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Batch delete error:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const addToExam = (question: any) => {
  // 这里可以实现快速加入试卷的功能
  ElMessage.info('加入试卷功能开发中...')
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'single_choice': return 'primary'
    case 'multiple_choice': return 'success'
    case 'true_false': return 'warning'
    case 'fill_blank': return 'info'
    default: return 'default'
  }
}

const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'single_choice': return '单选题'
    case 'multiple_choice': return '多选题'
    case 'true_false': return '判断题'
    case 'fill_blank': return '填空题'
    case 'short_answer': return '简答题'
    case 'essay': return '论述题'
    case 'calculation': return '计算题'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

onMounted(() => {
  loadQuestions()
})
</script>

<style scoped>
.question-bank {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
}

.header-left h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.header-left p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-content {
  padding: 20px;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-item:last-child {
  min-width: auto;
}

.questions-section .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8eaec;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-info h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.count-info {
  font-size: 14px;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.questions-list {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 400px;
}

.question-card {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
}

.question-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.question-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.question-number {
  font-weight: 600;
  color: #409eff;
}

.difficulty-stars {
  margin-left: 8px;
}

.question-actions {
  display: flex;
  gap: 4px;
}

.question-stem {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 12px;
}

.question-options {
  margin-bottom: 12px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 4px 0;
}

.option-item.correct {
  color: #67c23a;
  font-weight: 600;
}

.option-key {
  font-weight: 600;
  margin-right: 8px;
  min-width: 20px;
}

.option-content {
  flex: 1;
  line-height: 1.5;
}

.question-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #999;
}

.question-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.question-tag {
  background: #f0f9ff;
  color: #409eff;
  border: none;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e8eaec;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-item {
    min-width: auto;
  }
  
  .question-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .question-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .question-footer {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
</style>
