<template>
  <Layout>
    <div class="question-editor">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>{{ isEdit ? '编辑题目' : '添加题目' }}</h1>
        <p>使用富文本编辑器创建或编辑题目，支持公式、图片、表格等多种格式</p>
      </div>

      <!-- 题目编辑表单 -->
      <div class="editor-section">
        <div class="card">
          <el-form 
            ref="formRef" 
            :model="questionForm" 
            :rules="formRules" 
            label-width="120px"
            @submit.prevent="saveQuestion"
          >
            <!-- 基本信息 -->
            <div class="form-section">
              <h3>基本信息</h3>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="题目编号" prop="questionNumber">
                    <el-input v-model="questionForm.questionNumber" placeholder="如：1、2、3..." />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="题目类型" prop="questionType">
                    <el-select v-model="questionForm.questionType" placeholder="选择题目类型">
                      <el-option label="单选题" value="single_choice" />
                      <el-option label="多选题" value="multiple_choice" />
                      <el-option label="判断题" value="true_false" />
                      <el-option label="填空题" value="fill_blank" />
                      <el-option label="问答题" value="essay" />
                      <el-option label="计算题" value="calculation" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="学科" prop="subject">
                    <el-input v-model="questionForm.subject" placeholder="如：数学、物理、化学..." />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="难度等级" prop="difficultyLevel">
                    <el-select v-model="questionForm.difficultyLevel" placeholder="选择难度">
                      <el-option label="很简单" :value="1" />
                      <el-option label="简单" :value="2" />
                      <el-option label="中等" :value="3" />
                      <el-option label="困难" :value="4" />
                      <el-option label="很困难" :value="5" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 题目内容 -->
            <div class="form-section">
              <h3>题目内容</h3>
              
              <el-form-item label="题目描述" prop="stem" class="full-width">
                <RichTextEditor 
                  v-model="questionForm.stem"
                  placeholder="请输入题目描述，支持富文本、公式、图片等..."
                  height="200px"
                />
              </el-form-item>
            </div>

            <!-- 选择题选项 -->
            <div v-if="isChoiceQuestion" class="form-section">
              <h3>选项设置</h3>
              
              <div class="options-container">
                <div 
                  v-for="(option, index) in questionForm.options" 
                  :key="index"
                  class="option-item"
                >
                  <div class="option-header">
                    <span class="option-label">选项 {{ String.fromCharCode(65 + index) }}</span>
                    <el-button 
                      v-if="questionForm.options.length > 2"
                      type="danger" 
                      size="small" 
                      text
                      @click="removeOption(index)"
                    >
                      删除
                    </el-button>
                  </div>
                  <RichTextEditor 
                    v-model="questionForm.options[index]"
                    :placeholder="`请输入选项 ${String.fromCharCode(65 + index)} 的内容...`"
                    height="120px"
                  />
                </div>
                
                <el-button 
                  v-if="questionForm.options.length < 8"
                  type="primary" 
                  plain 
                  @click="addOption"
                  class="add-option-btn"
                >
                  <el-icon><Plus /></el-icon>
                  添加选项
                </el-button>
              </div>
            </div>

            <!-- 正确答案 -->
            <div class="form-section">
              <h3>正确答案</h3>
              
              <el-form-item v-if="isChoiceQuestion" label="正确选项" prop="correctAnswer">
                <el-checkbox-group 
                  v-if="questionForm.questionType === 'multiple_choice'"
                  v-model="correctAnswerArray"
                >
                  <el-checkbox 
                    v-for="(option, index) in questionForm.options" 
                    :key="index"
                    :label="String.fromCharCode(65 + index)"
                  >
                    选项 {{ String.fromCharCode(65 + index) }}
                  </el-checkbox>
                </el-checkbox-group>
                
                <el-radio-group 
                  v-else
                  v-model="questionForm.correctAnswer"
                >
                  <el-radio 
                    v-for="(option, index) in questionForm.options" 
                    :key="index"
                    :label="String.fromCharCode(65 + index)"
                  >
                    选项 {{ String.fromCharCode(65 + index) }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item v-else label="参考答案" prop="correctAnswer" class="full-width">
                <RichTextEditor 
                  v-model="questionForm.correctAnswer"
                  placeholder="请输入参考答案..."
                  height="150px"
                />
              </el-form-item>
            </div>

            <!-- 解析说明 -->
            <div class="form-section">
              <h3>解析说明</h3>
              
              <el-form-item label="答案解析" class="full-width">
                <RichTextEditor 
                  v-model="questionForm.explanation"
                  placeholder="请输入答案解析（可选）..."
                  height="150px"
                />
              </el-form-item>
            </div>

            <!-- 标签设置 -->
            <div class="form-section">
              <h3>标签设置</h3>
              
              <el-form-item label="题目标签">
                <el-tag
                  v-for="tag in questionForm.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                
                <el-input
                  v-if="tagInputVisible"
                  ref="tagInputRef"
                  v-model="tagInputValue"
                  size="small"
                  @keyup.enter="addTag"
                  @blur="addTag"
                  class="tag-input"
                />
                
                <el-button 
                  v-else
                  size="small" 
                  @click="showTagInput"
                  class="add-tag-btn"
                >
                  <el-icon><Plus /></el-icon>
                  添加标签
                </el-button>
              </el-form-item>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
              <el-button @click="goBack">取消</el-button>
              <el-button @click="previewQuestion">预览</el-button>
              <el-button 
                type="primary" 
                :loading="saving"
                @click="saveQuestion"
              >
                {{ isEdit ? '更新题目' : '保存题目' }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>

      <!-- 题目预览对话框 -->
      <el-dialog v-model="previewVisible" title="题目预览" width="800px">
        <div class="question-preview">
          <div class="preview-header">
            <span class="question-number">{{ questionForm.questionNumber }}.</span>
            <el-tag :type="getQuestionTypeColor(questionForm.questionType)" size="small">
              {{ getQuestionTypeText(questionForm.questionType) }}
            </el-tag>
            <el-tag type="info" size="small">{{ questionForm.subject }}</el-tag>
            <el-rate 
              v-model="questionForm.difficultyLevel" 
              disabled 
              show-score 
              text-color="#ff9900"
            />
          </div>
          
          <div class="preview-content">
            <div class="question-stem" v-html="questionForm.stem"></div>
            
            <div v-if="isChoiceQuestion && questionForm.options.length > 0" class="question-options">
              <div 
                v-for="(option, index) in questionForm.options" 
                :key="index"
                class="option-preview"
              >
                <span class="option-label">{{ String.fromCharCode(65 + index) }}.</span>
                <span class="option-content" v-html="option"></span>
              </div>
            </div>
            
            <div class="question-answer">
              <h4>正确答案：</h4>
              <div v-html="questionForm.correctAnswer"></div>
            </div>
            
            <div v-if="questionForm.explanation" class="question-explanation">
              <h4>解析：</h4>
              <div v-html="questionForm.explanation"></div>
            </div>
            
            <div v-if="questionForm.tags.length > 0" class="question-tags">
              <h4>标签：</h4>
              <el-tag v-for="tag in questionForm.tags" :key="tag" size="small" class="tag-item">
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import RichTextEditor from '@/components/RichTextEditor.vue'
import { http } from '@/utils/http'

// 路由和响应式数据
const route = useRoute()
const router = useRouter()
const formRef = ref()
const tagInputRef = ref()

const isEdit = computed(() => !!route.params.id)
const saving = ref(false)
const previewVisible = ref(false)
const tagInputVisible = ref(false)
const tagInputValue = ref('')

// 题目表单数据
const questionForm = reactive({
  questionNumber: '',
  questionType: 'single_choice',
  subject: '',
  difficultyLevel: 3,
  stem: '',
  options: ['', ''],
  correctAnswer: '',
  explanation: '',
  tags: []
})

// 多选题正确答案数组
const correctAnswerArray = ref([])

// 表单验证规则
const formRules = {
  questionNumber: [
    { required: true, message: '请输入题目编号', trigger: 'blur' }
  ],
  questionType: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请输入学科', trigger: 'blur' }
  ],
  stem: [
    { required: true, message: '请输入题目描述', trigger: 'blur' }
  ],
  correctAnswer: [
    { required: true, message: '请设置正确答案', trigger: 'blur' }
  ]
}

// 计算属性
const isChoiceQuestion = computed(() => {
  return ['single_choice', 'multiple_choice', 'true_false'].includes(questionForm.questionType)
})

// 监听题目类型变化
watch(() => questionForm.questionType, (newType) => {
  if (newType === 'true_false') {
    questionForm.options = ['正确', '错误']
  } else if (newType === 'single_choice' || newType === 'multiple_choice') {
    if (questionForm.options.length < 2) {
      questionForm.options = ['', '']
    }
  } else {
    questionForm.options = []
  }
  questionForm.correctAnswer = ''
  correctAnswerArray.value = []
})

// 监听多选题答案变化
watch(correctAnswerArray, (newValue) => {
  if (questionForm.questionType === 'multiple_choice') {
    questionForm.correctAnswer = newValue.join(',')
  }
})

// 选项操作
const addOption = () => {
  questionForm.options.push('')
}

const removeOption = (index: number) => {
  questionForm.options.splice(index, 1)
  // 重新设置正确答案
  questionForm.correctAnswer = ''
  correctAnswerArray.value = []
}

// 标签操作
const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = tagInputValue.value.trim()
  if (tag && !questionForm.tags.includes(tag)) {
    questionForm.tags.push(tag)
  }
  tagInputValue.value = ''
  tagInputVisible.value = false
}

const removeTag = (tag: string) => {
  const index = questionForm.tags.indexOf(tag)
  if (index > -1) {
    questionForm.tags.splice(index, 1)
  }
}

// 获取题目类型颜色
const getQuestionTypeColor = (type: string) => {
  const colors = {
    single_choice: 'primary',
    multiple_choice: 'success',
    true_false: 'warning',
    fill_blank: 'info',
    essay: 'danger',
    calculation: 'primary'
  }
  return colors[type] || 'info'
}

// 获取题目类型文本
const getQuestionTypeText = (type: string) => {
  const texts = {
    single_choice: '单选题',
    multiple_choice: '多选题',
    true_false: '判断题',
    fill_blank: '填空题',
    essay: '问答题',
    calculation: '计算题'
  }
  return texts[type] || '未知类型'
}

// 预览题目
const previewQuestion = () => {
  previewVisible.value = true
}

// 保存题目
const saveQuestion = async () => {
  try {
    await formRef.value.validate()
    
    saving.value = true
    
    const questionData = {
      ...questionForm,
      options: isChoiceQuestion.value ? questionForm.options.filter(opt => opt.trim()) : []
    }
    
    if (isEdit.value) {
      await http.put(`/question/${route.params.id}`, questionData)
      ElMessage.success('题目更新成功')
    } else {
      await http.post('/question', questionData)
      ElMessage.success('题目保存成功')
    }
    
    router.push('/question-bank')
    
  } catch (error) {
    console.error('Save question error:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 返回
const goBack = () => {
  router.back()
}

// 加载题目数据（编辑模式）
const loadQuestion = async () => {
  if (!isEdit.value) return
  
  try {
    const response = await http.get(`/question/${route.params.id}`)
    const question = response.question
    
    Object.assign(questionForm, {
      questionNumber: question.questionNumber,
      questionType: question.questionType,
      subject: question.subject,
      difficultyLevel: question.difficultyLevel,
      stem: question.stem,
      options: question.options || [],
      correctAnswer: question.correctAnswer,
      explanation: question.explanation || '',
      tags: question.tags || []
    })
    
    // 设置多选题答案
    if (question.questionType === 'multiple_choice' && question.correctAnswer) {
      correctAnswerArray.value = question.correctAnswer.split(',')
    }
    
  } catch (error) {
    console.error('Load question error:', error)
    ElMessage.error('加载题目失败')
    router.push('/question-bank')
  }
}

// 页面加载
onMounted(() => {
  loadQuestion()
})
</script>

<style scoped>
.question-editor {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.full-width :deep(.el-form-item__content) {
  width: 100%;
}

.options-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-item {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.option-label {
  font-weight: 600;
  color: #303133;
}

.add-option-btn {
  margin-top: 8px;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 120px;
  margin-right: 8px;
}

.add-tag-btn {
  margin-bottom: 8px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.question-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.question-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.preview-content {
  line-height: 1.6;
}

.question-stem {
  margin-bottom: 16px;
  font-size: 16px;
}

.question-options {
  margin: 16px 0;
}

.option-preview {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.option-label {
  margin-right: 8px;
  font-weight: 600;
  min-width: 20px;
}

.option-content {
  flex: 1;
}

.question-answer,
.question-explanation,
.question-tags {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.question-answer h4,
.question-explanation h4,
.question-tags h4 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}
</style>
