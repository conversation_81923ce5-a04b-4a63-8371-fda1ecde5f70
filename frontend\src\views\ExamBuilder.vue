<template>
  <Layout>
    <div class="exam-builder">
      <div class="page-header">
        <h2>智能组卷</h2>
        <p>从题库中选择题目，快速生成个性化试卷</p>
      </div>

      <div class="builder-container">
        <!-- 左侧：题目选择区 -->
        <div class="question-selector">
          <div class="card">
            <div class="card-header">
              <h3>题目选择</h3>
              <div class="filter-actions">
                <el-button size="small" @click="resetFilters">
                  <el-icon><Refresh /></el-icon>
                  重置筛选
                </el-button>
              </div>
            </div>
            
            <!-- 筛选条件 -->
            <div class="filter-section">
              <div class="filter-row">
                <el-select v-model="filters.questionType" placeholder="题目类型" size="small" @change="loadQuestions">
                  <el-option label="全部" value="" />
                  <el-option label="单选题" value="single_choice" />
                  <el-option label="多选题" value="multiple_choice" />
                  <el-option label="判断题" value="true_false" />
                  <el-option label="填空题" value="fill_blank" />
                  <el-option label="简答题" value="short_answer" />
                </el-select>
                
                <el-select v-model="filters.subject" placeholder="学科" size="small" @change="loadQuestions">
                  <el-option label="全部" value="" />
                  <el-option v-for="subject in subjects" :key="subject" :label="subject" :value="subject" />
                </el-select>
                
                <el-select v-model="filters.difficultyLevel" placeholder="难度" size="small" @change="loadQuestions">
                  <el-option label="全部" value="" />
                  <el-option label="很简单" :value="1" />
                  <el-option label="简单" :value="2" />
                  <el-option label="中等" :value="3" />
                  <el-option label="困难" :value="4" />
                  <el-option label="很困难" :value="5" />
                </el-select>
              </div>
            </div>
            
            <!-- 题目列表 -->
            <div class="questions-list" v-loading="questionsLoading">
              <div
                v-for="question in availableQuestions"
                :key="question.id"
                class="question-item"
                @click="addQuestionToExam(question)"
              >
                <div class="question-header">
                  <span class="question-number">{{ question.questionNumber || '未编号' }}</span>
                  <el-tag :type="getQuestionTypeColor(question.questionType)" size="small">
                    {{ getQuestionTypeText(question.questionType) }}
                  </el-tag>
                  <el-button type="primary" size="small" @click.stop="addQuestionToExam(question)">
                    <el-icon><Plus /></el-icon>
                    添加
                  </el-button>
                </div>
                <div class="question-stem">{{ question.stem.substring(0, 100) }}...</div>
              </div>
              
              <el-empty v-if="!questionsLoading && availableQuestions.length === 0" description="暂无题目" />
            </div>
          </div>
        </div>

        <!-- 右侧：试卷构建区 -->
        <div class="exam-builder-panel">
          <div class="card">
            <div class="card-header">
              <h3>试卷构建</h3>
              <div class="exam-actions">
                <el-button @click="showExamInfoDialog">
                  <el-icon><Setting /></el-icon>
                  试卷设置
                </el-button>
                <el-button type="primary" @click="saveExam" :disabled="examQuestions.length === 0">
                  <el-icon><Document /></el-icon>
                  保存试卷
                </el-button>
              </div>
            </div>
            
            <!-- 试卷信息 -->
            <div class="exam-info">
              <div class="info-item">
                <strong>试卷标题：</strong>{{ examInfo.title || '未设置' }}
              </div>
              <div class="info-item">
                <strong>题目数量：</strong>{{ examQuestions.length }} 道
              </div>
              <div class="info-item">
                <strong>总分：</strong>{{ totalScore }} 分
              </div>
            </div>
            
            <!-- 试卷题目列表 -->
            <div class="exam-questions" v-if="examQuestions.length > 0">
              <draggable
                v-model="examQuestions"
                item-key="id"
                @end="onQuestionReorder"
              >
                <template #item="{ element: question, index }">
                  <div class="exam-question-item">
                    <div class="question-order">{{ index + 1 }}</div>
                    <div class="question-content">
                      <div class="question-header">
                        <el-tag :type="getQuestionTypeColor(question.questionType)" size="small">
                          {{ getQuestionTypeText(question.questionType) }}
                        </el-tag>
                        <div class="question-actions">
                          <el-input-number
                            v-model="question.score"
                            :min="1"
                            :max="100"
                            size="small"
                            @change="updateTotalScore"
                          />
                          <span class="score-label">分</span>
                          <el-button
                            type="danger"
                            size="small"
                            @click="removeQuestionFromExam(index)"
                          >
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                      <div class="question-stem">{{ question.stem }}</div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            
            <el-empty v-else description="请从左侧添加题目" />
          </div>
        </div>
      </div>

      <!-- 试卷信息设置对话框 -->
      <el-dialog v-model="examInfoDialogVisible" title="试卷设置" width="500px">
        <el-form :model="examInfo" label-width="80px">
          <el-form-item label="试卷标题" required>
            <el-input v-model="examInfo.title" placeholder="请输入试卷标题" />
          </el-form-item>
          <el-form-item label="试卷描述">
            <el-input v-model="examInfo.description" type="textarea" :rows="3" placeholder="请输入试卷描述" />
          </el-form-item>
          <el-form-item label="学科">
            <el-input v-model="examInfo.subject" placeholder="请输入学科" />
          </el-form-item>
          <el-form-item label="考试时间">
            <el-input-number v-model="examInfo.timeLimit" :min="1" :max="300" />
            <span style="margin-left: 8px;">分钟</span>
          </el-form-item>
          <el-form-item label="考试说明">
            <el-input v-model="examInfo.instructions" type="textarea" :rows="3" placeholder="请输入考试说明" />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <el-button @click="examInfoDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveExamInfo">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import draggable from 'vuedraggable'
import {
  Plus,
  Delete,
  Document,
  Setting,
  Refresh
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { http } from '@/utils/http'

const router = useRouter()

const questionsLoading = ref(false)
const examInfoDialogVisible = ref(false)
const availableQuestions = ref([])
const examQuestions = ref([])
const subjects = ref([])

const filters = reactive({
  questionType: '',
  subject: '',
  difficultyLevel: ''
})

const examInfo = reactive({
  title: '',
  description: '',
  subject: '',
  timeLimit: 120,
  instructions: ''
})

const totalScore = computed(() => {
  return examQuestions.value.reduce((sum, q) => sum + (q.score || 1), 0)
})

const loadQuestions = async () => {
  questionsLoading.value = true
  try {
    const params = {
      limit: 50,
      ...filters
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })
    
    const response = await http.get('/question', { params })
    availableQuestions.value = response.questions || []
    
    // 提取学科列表
    const subjectSet = new Set()
    availableQuestions.value.forEach(q => {
      if (q.subject) subjectSet.add(q.subject)
    })
    subjects.value = Array.from(subjectSet)
    
  } catch (error) {
    console.error('Load questions error:', error)
    ElMessage.error('加载题目失败')
  } finally {
    questionsLoading.value = false
  }
}

const resetFilters = () => {
  Object.keys(filters).forEach(key => {
    filters[key] = ''
  })
  loadQuestions()
}

const addQuestionToExam = (question: any) => {
  // 检查是否已存在
  const exists = examQuestions.value.find(q => q.id === question.id)
  if (exists) {
    ElMessage.warning('题目已存在于试卷中')
    return
  }
  
  examQuestions.value.push({
    ...question,
    score: 1 // 默认分值
  })
  
  ElMessage.success('题目已添加到试卷')
}

const removeQuestionFromExam = (index: number) => {
  examQuestions.value.splice(index, 1)
  ElMessage.success('题目已从试卷中移除')
}

const onQuestionReorder = () => {
  ElMessage.success('题目顺序已调整')
}

const updateTotalScore = () => {
  // 总分会自动计算
}

const showExamInfoDialog = () => {
  examInfoDialogVisible.value = true
}

const saveExamInfo = () => {
  if (!examInfo.title.trim()) {
    ElMessage.error('请输入试卷标题')
    return
  }
  
  examInfoDialogVisible.value = false
  ElMessage.success('试卷信息已保存')
}

const saveExam = async () => {
  if (!examInfo.title.trim()) {
    ElMessage.error('请先设置试卷标题')
    showExamInfoDialog()
    return
  }
  
  if (examQuestions.value.length === 0) {
    ElMessage.error('请至少添加一道题目')
    return
  }
  
  try {
    const examData = {
      ...examInfo,
      totalScore: totalScore.value,
      questions: examQuestions.value.map((q, index) => ({
        questionId: q.id,
        questionOrder: index + 1,
        score: q.score || 1
      }))
    }
    
    const response = await http.post('/exam', examData)
    ElMessage.success('试卷保存成功')
    
    // 跳转到试卷列表
    router.push('/exam-list')
  } catch (error) {
    console.error('Save exam error:', error)
    ElMessage.error('保存试卷失败')
  }
}

const getQuestionTypeColor = (type: string) => {
  switch (type) {
    case 'single_choice': return 'primary'
    case 'multiple_choice': return 'success'
    case 'true_false': return 'warning'
    case 'fill_blank': return 'info'
    default: return 'default'
  }
}

const getQuestionTypeText = (type: string) => {
  switch (type) {
    case 'single_choice': return '单选题'
    case 'multiple_choice': return '多选题'
    case 'true_false': return '判断题'
    case 'fill_blank': return '填空题'
    case 'short_answer': return '简答题'
    case 'essay': return '论述题'
    case 'calculation': return '计算题'
    default: return '未知'
  }
}

onMounted(() => {
  loadQuestions()
})
</script>

<style scoped>
.exam-builder {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.builder-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: calc(100vh - 200px);
}

.question-selector,
.exam-builder-panel {
  display: flex;
  flex-direction: column;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8eaec;
}

.card-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.filter-section {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-row {
  display: flex;
  gap: 12px;
}

.questions-list {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question-item {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.question-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.question-number {
  font-weight: 600;
  color: #409eff;
}

.question-stem {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.exam-info {
  padding: 16px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e8eaec;
}

.info-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.exam-questions {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.exam-question-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
}

.question-order {
  width: 24px;
  height: 24px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.question-content {
  flex: 1;
}

.question-content .question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.question-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.score-label {
  font-size: 12px;
  color: #666;
}

@media (max-width: 1200px) {
  .builder-container {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .card {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
  }
  
  .question-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .question-content .question-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .question-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
