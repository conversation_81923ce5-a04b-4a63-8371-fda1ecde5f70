import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { ElMessage } from 'element-plus'
import { authApi } from '@/api/auth'
import { tokenManager } from '@/utils/tokenManager'
import type { User, LoginRequest, RegisterRequest } from '@/types/auth'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(tokenManager.getToken())
  const loading = ref(false)

  const isAuthenticated = computed(() => !!token.value && !!user.value && !tokenManager.isTokenExpired())
  const isPremium = computed(() => user.value?.userType === 'premium' || user.value?.userType === 'admin')
  const isAdmin = computed(() => user.value?.userType === 'admin')

  // 设置认证信息
  const setAuth = (authToken: string, userData: User, expiresIn?: string) => {
    token.value = authToken
    user.value = userData
    tokenManager.setToken(authToken, expiresIn)
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = null
    user.value = null
    tokenManager.clearToken()
  }

  // 登录
  const login = async (credentials: LoginRequest) => {
    loading.value = true
    try {
      const response = await authApi.login(credentials)
      setAuth(response.token, response.user)
      ElMessage.success('登录成功')
      return response
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: RegisterRequest) => {
    loading.value = true
    try {
      const response = await authApi.register(userData)

      // 如果需要邮箱验证，不设置认证状态
      if (response.requiresEmailVerification) {
        ElMessage.success(response.message || '注册成功，请检查邮箱验证')
        return response
      }

      // 如果不需要邮箱验证，直接设置认证状态
      if (response.token) {
        setAuth(response.token, response.user)
        ElMessage.success('注册成功')
      }

      return response
    } catch (error: any) {
      ElMessage.error(error.response?.data?.error || error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuth()
      ElMessage.success('已退出登录')
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async () => {
    if (!token.value) return null
    
    try {
      const response = await authApi.getCurrentUser()
      user.value = response.user
      return response.user
    } catch (error) {
      console.error('Get current user error:', error)
      clearAuth()
      return null
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    // 检查token是否过期
    if (tokenManager.isTokenExpired()) {
      clearAuth()
      return
    }

    if (token.value && !user.value) {
      await getCurrentUser()
    }
  }

  // 获取token剩余时间
  const getTokenRemainingTime = () => {
    return tokenManager.getTokenRemainingTimeFormatted()
  }

  // 检查是否需要刷新token
  const shouldRefreshToken = () => {
    return tokenManager.shouldRefreshToken()
  }

  // 刷新令牌
  const refreshToken = async () => {
    if (!token.value) return false

    try {
      const response = await authApi.refreshToken(token.value)
      token.value = response.token
      tokenManager.setToken(response.token)
      return true
    } catch (error) {
      console.error('Refresh token error:', error)
      clearAuth()
      return false
    }
  }

  // 修改密码
  const changePassword = async (currentPassword: string, newPassword: string) => {
    loading.value = true
    try {
      await authApi.changePassword({ currentPassword, newPassword })
      ElMessage.success('密码修改成功')
    } catch (error: any) {
      ElMessage.error(error.message || '密码修改失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 检查功能使用权限
  const canUseFeature = (feature: 'pdfConvert' | 'questionParse' | 'examGenerate'): boolean => {
    if (!user.value) return false
    
    if (user.value.userType === 'admin') return true
    if (user.value.userType === 'premium') return true
    
    // 免费用户检查每日使用限制
    const limits = {
      pdfConvert: 1,
      questionParse: 1,
      examGenerate: 1
    }
    
    return (user.value.dailyUsage?.[feature] || 0) < limits[feature]
  }

  // 获取功能使用情况
  const getFeatureUsage = (feature: 'pdfConvert' | 'questionParse' | 'examGenerate') => {
    if (!user.value) return { used: 0, limit: 0 }
    
    if (user.value.userType === 'admin' || user.value.userType === 'premium') {
      return { used: user.value.dailyUsage?.[feature] || 0, limit: -1 } // -1 表示无限制
    }
    
    const limits = {
      pdfConvert: 1,
      questionParse: 1,
      examGenerate: 1
    }
    
    return {
      used: user.value.dailyUsage?.[feature] || 0,
      limit: limits[feature]
    }
  }

  return {
    user: readonly(user),
    token: readonly(token),
    loading: readonly(loading),
    isAuthenticated,
    isPremium,
    isAdmin,
    login,
    register,
    logout,
    getCurrentUser,
    checkAuth,
    refreshToken,
    changePassword,
    canUseFeature,
    getFeatureUsage
  }
})
