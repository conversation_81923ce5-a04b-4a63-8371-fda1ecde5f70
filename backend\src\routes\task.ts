import { Router } from 'express';
import { TaskController } from '../controllers/TaskController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const taskController = new TaskController();

// 获取用户任务列表
router.get('/', authenticateToken, taskController.getUserTasks.bind(taskController));

// 获取任务详情
router.get('/:taskId', authenticateToken, taskController.getTaskById.bind(taskController));

// 获取任务状态
router.get('/:taskId/status', authenticateToken, taskController.getTaskStatus.bind(taskController));

// 取消任务
router.post('/:taskId/cancel', authenticateToken, taskController.cancelTask.bind(taskController));

// 重试失败的任务
router.post('/:taskId/retry', authenticateToken, taskController.retryTask.bind(taskController));

// 获取任务结果
router.get('/:taskId/result', authenticateToken, taskController.getTaskResult.bind(taskController));

// 下载任务结果文件
router.get('/:taskId/download', authenticateToken, taskController.downloadTaskResult);

export default router;
