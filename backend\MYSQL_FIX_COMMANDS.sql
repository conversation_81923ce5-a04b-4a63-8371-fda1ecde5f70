-- 🔧 MySQL权限修复命令
-- 请在MySQL管理工具中执行以下命令

-- 1. 查看当前用户权限
SELECT user, host, authentication_string FROM mysql.user WHERE user = 'root';

-- 2. 为实际连接的IP地址添加权限
GRANT ALL PRIVILEGES ON *.* TO 'root'@'**************' IDENTIFIED BY '1qaz!@#$lymysql' WITH GRANT OPTION;

-- 3. 为域名对应的IP添加权限（如果不同）
GRANT ALL PRIVILEGES ON *.* TO 'root'@'**************' IDENTIFIED BY '1qaz!@#$lymysql' WITH GRANT OPTION;

-- 4. 临时解决方案：允许所有IP访问（仅开发环境）
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY '1qaz!@#$lymysql' WITH GRANT OPTION;

-- 5. 确保数据库存在
CREATE DATABASE IF NOT EXISTS `d2x_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 6. 刷新权限
FLUSH PRIVILEGES;

-- 7. 验证权限
SELECT user, host FROM mysql.user WHERE user = 'root';

-- 8. 测试连接
SELECT CONNECTION_ID(), USER(), @@hostname, @@version;
