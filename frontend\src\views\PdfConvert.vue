<template>
  <Layout>
    <div class="pdf-convert">
      <div class="page-header">
        <h2>PDF转Word</h2>
        <p>将PDF文档快速转换为可编辑的Word格式</p>
      </div>

      <!-- 使用限制提示 -->
      <div class="usage-alert" v-if="!authStore.canUseFeature('pdfConvert')">
        <el-alert
          title="使用次数已达上限"
          type="warning"
          :description="`免费用户每日可转换${usage.limit}次，您今日已使用${usage.used}次。升级到高级会员可享受无限次转换。`"
          show-icon
          :closable="false"
        />
      </div>

      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="onUploadSuccess"
          :on-error="onUploadError"
          :on-progress="onUploadProgress"
          :disabled="uploading || !authStore.canUseFeature('pdfConvert')"
          accept=".pdf"
          :show-file-list="false"
        >
          <div class="upload-content">
            <el-icon class="upload-icon" size="48">
              <UploadFilled v-if="!uploading" />
              <Loading v-else />
            </el-icon>
            <div class="upload-text">
              <p v-if="!uploading">点击或拖拽PDF文件到此处上传</p>
              <p v-else>正在上传文件...</p>
            </div>
            <div class="upload-hint">
              支持PDF格式，文件大小不超过50MB
            </div>
          </div>
        </el-upload>

        <!-- 上传进度 -->
        <div v-if="uploading" class="progress-section">
          <el-progress
            :percentage="uploadProgress"
            :status="uploadProgress === 100 ? 'success' : undefined"
          />
          <p class="progress-text">{{ uploadProgressText }}</p>
        </div>
      </div>

      <!-- 转换选项 -->
      <div class="options-section" v-if="!uploading">
        <div class="card">
          <div class="card-header">
            <h3>转换选项</h3>
          </div>
          <div class="options-grid">
            <div class="option-item">
              <el-radio-group v-model="convertOptions.mode">
                <el-radio label="basic">
                  <div class="option-content">
                    <div class="option-title">基础版</div>
                    <div class="option-desc">适用于纯文本和简单排版的PDF</div>
                  </div>
                </el-radio>
                <el-radio label="advanced" :disabled="!authStore.isPremium">
                  <div class="option-content">
                    <div class="option-title">
                      高级版
                      <el-tag v-if="!authStore.isPremium" type="warning" size="small">需要高级会员</el-tag>
                    </div>
                    <div class="option-desc">支持复杂公式、图表和表格的精确识别</div>
                  </div>
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 转换历史 -->
      <div class="history-section">
        <div class="card">
          <div class="card-header">
            <h3>转换历史</h3>
            <el-button @click="loadHistory" :loading="historyLoading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          
          <el-table :data="historyList" v-loading="historyLoading">
            <el-table-column prop="originalFilename" label="文件名" min-width="200">
              <template #default="{ row }">
                <div class="filename">
                  <el-icon><Document /></el-icon>
                  <span :title="row.originalFilename">{{ formatFilename(row.originalFilename) }}</span>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="fileSize" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>
            
            <el-table-column prop="status" label="状态" width="120">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column prop="createdAt" label="上传时间" width="160">
              <template #default="{ row }">
                {{ formatTime(row.createdAt) }}
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button
                    v-if="row.status === 'completed'"
                    type="primary"
                    size="small"
                    @click="downloadFile(row.id)"
                  >
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                  
                  <el-button
                    v-if="row.status === 'processing'"
                    type="info"
                    size="small"
                    @click="checkTaskStatus(row.taskId)"
                  >
                    <el-icon><View /></el-icon>
                    查看进度
                  </el-button>
                  
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteFile(row.id)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="pagination.total > 0">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadHistory"
              @current-change="loadHistory"
            />
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  UploadFilled,
  Loading,
  Document,
  Refresh,
  Download,
  View,
  Delete
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import { http, downloadFile as httpDownloadFile } from '@/utils/http'

// 文件历史记录类型
interface FileHistoryItem {
  id: string
  originalFilename: string
  filename: string
  fileSize: number
  status: string
  uploadType: string
  createdAt: string
  updatedAt: string
}

const authStore = useAuthStore()

const uploading = ref(false)
const uploadProgress = ref(0)
const uploadProgressText = ref('')
const historyLoading = ref(false)
const historyList = ref<FileHistoryItem[]>([])

const convertOptions = ref({
  mode: 'basic'
})

const pagination = ref({
  page: 1,
  limit: 10,
  total: 0
})

const uploadAction = import.meta.env.VITE_API_BASE_URL + '/file/upload/pdf-convert'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${authStore.token}`
}))

const usage = computed(() => authStore.getFeatureUsage('pdfConvert'))

const beforeUpload = (file: File) => {
  const isPDF = file.type === 'application/pdf'
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isPDF) {
    ElMessage.error('只能上传PDF格式的文件！')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB！')
    return false
  }

  uploading.value = true
  uploadProgress.value = 0
  uploadProgressText.value = '准备上传...'
  
  return true
}

const onUploadProgress = (event: any) => {
  uploadProgress.value = Math.round((event.loaded / event.total) * 100)
  uploadProgressText.value = `上传进度: ${uploadProgress.value}%`
}

const onUploadSuccess = (response: any) => {
  uploading.value = false
  ElMessage.success('文件上传成功，正在处理中...')
  loadHistory()
  
  // 重置用户使用次数（这里应该从后端获取最新的用户信息）
  authStore.getCurrentUser()
}

const onUploadError = (error: any) => {
  uploading.value = false
  console.error('Upload error:', error)
  ElMessage.error('文件上传失败，请重试')
}

const loadHistory = async () => {
  historyLoading.value = true
  try {
    const response = await http.get('/file/uploads', {
      params: {
        page: pagination.value.page,
        limit: pagination.value.limit,
        uploadType: 'pdf_convert'
      }
    })

    console.log('📋 加载历史记录响应:', response)
    console.log('📁 文件列表:', response.files)

    historyList.value = response.files
    pagination.value.total = response.pagination.total

    // 检查文件名编码
    if (response.files && response.files.length > 0) {
      console.log('🔍 第一个文件的原始文件名:', response.files[0].originalFilename)
      console.log('🔍 文件名编码检查:', {
        raw: response.files[0].originalFilename,
        encoded: encodeURIComponent(response.files[0].originalFilename),
        decoded: decodeURIComponent(response.files[0].originalFilename)
      })
    }
  } catch (error) {
    console.error('Load history error:', error)
  } finally {
    historyLoading.value = false
  }
}

const checkTaskStatus = async (taskId: string) => {
  try {
    const response = await http.get(`/task/${taskId}/status`)
    ElMessage.info(`任务状态: ${getStatusText(response.status)}, 进度: ${response.progress}%`)
  } catch (error) {
    console.error('Check task status error:', error)
  }
}

const downloadFile = async (fileId: string) => {
  try {
    console.log('开始下载文件，ID:', fileId)
    console.log('当前历史列表:', historyList.value)

    // 从历史列表中找到对应的文件信息
    const file = historyList.value.find(f => f.id === fileId)
    console.log('找到的文件信息:', file)

    let filename = 'converted.docx'
    if (file && file.originalFilename) {
      // 移除原文件扩展名，添加.docx
      filename = `${file.originalFilename.replace(/\.[^/.]+$/, '')}.docx`
    }

    console.log('下载文件名:', filename)
    await httpDownloadFile(`/file/download/${fileId}`, filename)
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('Download error:', error)
    ElMessage.error('文件下载失败')
  }
}

const deleteFile = async (fileId: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文件吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await http.delete(`/file/uploads/${fileId}`)
    ElMessage.success('文件删除成功')
    loadHistory()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete error:', error)
    }
  }
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / 1024 / 1024).toFixed(1)} MB`
}

// 格式化文件名，处理编码问题
const formatFilename = (filename: string) => {
  if (!filename) return '未知文件'

  try {
    // 尝试解码可能被错误编码的文件名
    if (filename.includes('%')) {
      return decodeURIComponent(filename)
    }

    // 检查是否是乱码（包含特殊字符）
    if (/[^\x00-\x7F]/.test(filename) && !/[\u4e00-\u9fa5]/.test(filename)) {
      // 可能是编码问题，尝试重新编码
      try {
        return decodeURIComponent(escape(filename))
      } catch {
        return filename
      }
    }

    return filename
  } catch (error) {
    console.warn('文件名格式化失败:', error)
    return filename || '未知文件'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'processing': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'processing': return '处理中'
    case 'failed': return '失败'
    case 'uploaded': return '已上传'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.pdf-convert {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.usage-alert {
  margin-bottom: 20px;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-dragger {
  width: 100%;
}

.upload-content {
  padding: 40px;
  text-align: center;
}

.upload-icon {
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: #909399;
}

.progress-section {
  margin-top: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.options-section {
  margin-bottom: 30px;
}

.options-grid {
  padding: 20px 0;
}

.option-item {
  margin-bottom: 20px;
}

.option-content {
  margin-left: 8px;
}

.option-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-desc {
  font-size: 14px;
  color: #666;
}

.history-section .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8eaec;
}

.card-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.filename {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .upload-content {
    padding: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
