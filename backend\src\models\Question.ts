import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';

// 题目类型
export type QuestionType = 
  | 'single_choice'    // 单选题
  | 'multiple_choice'  // 多选题
  | 'true_false'       // 判断题
  | 'fill_blank'       // 填空题
  | 'short_answer'     // 简答题
  | 'essay'            // 论述题
  | 'calculation';     // 计算题

// 选项接口
export interface QuestionOption {
  key: string;        // 选项标识 (A, B, C, D)
  content: string;    // 选项内容
  isCorrect?: boolean; // 是否为正确答案（多选题使用）
}

// 题目属性接口
export interface QuestionAttributes {
  id: number;
  userId: number;
  questionNumber?: string;
  questionType: QuestionType;
  subject?: string;
  difficultyLevel: number; // 1-5，1最简单，5最困难
  stem: string; // 题干
  options?: QuestionOption[]; // 选项（选择题使用）
  correctAnswer?: string; // 正确答案
  explanation?: string; // 解析
  tags: string[]; // 标签
  sourceFileId?: number; // 来源文件ID
  createdAt: Date;
  updatedAt: Date;
}

// 创建题目时的可选属性
export interface QuestionCreationAttributes extends Optional<QuestionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// Question模型类
class Question extends Model<QuestionAttributes, QuestionCreationAttributes> implements QuestionAttributes {
  public id!: number;
  public userId!: number;
  public questionNumber?: string;
  public questionType!: QuestionType;
  public subject?: string;
  public difficultyLevel!: number;
  public stem!: string;
  public options?: QuestionOption[];
  public correctAnswer?: string;
  public explanation?: string;
  public tags!: string[];
  public sourceFileId?: number;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联的用户
  public User?: User;

  // 验证题目数据
  public validateQuestion(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证题干
    if (!this.stem || this.stem.trim().length < 5) {
      errors.push('题干长度至少5个字符');
    }

    // 验证选择题选项
    if (['single_choice', 'multiple_choice', 'true_false'].includes(this.questionType)) {
      if (!this.options || this.options.length < 2) {
        errors.push('选择题至少需要2个选项');
      }

      if (this.options) {
        // 验证选项内容
        const emptyOptions = this.options.filter(opt => !opt.content.trim());
        if (emptyOptions.length > 0) {
          errors.push('所有选项都必须有内容');
        }

        // 验证正确答案
        if (this.questionType === 'single_choice') {
          if (!this.correctAnswer) {
            errors.push('单选题必须指定正确答案');
          }
        } else if (this.questionType === 'multiple_choice') {
          const correctOptions = this.options.filter(opt => opt.isCorrect);
          if (correctOptions.length === 0) {
            errors.push('多选题至少需要一个正确答案');
          }
        }
      }
    }

    // 验证难度等级
    if (this.difficultyLevel < 1 || this.difficultyLevel > 5) {
      errors.push('难度等级必须在1-5之间');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 获取题目类型的中文名称
  public getQuestionTypeName(): string {
    const typeNames = {
      single_choice: '单选题',
      multiple_choice: '多选题',
      true_false: '判断题',
      fill_blank: '填空题',
      short_answer: '简答题',
      essay: '论述题',
      calculation: '计算题'
    };
    return typeNames[this.questionType] || '未知类型';
  }

  // 获取难度等级的中文名称
  public getDifficultyName(): string {
    const difficultyNames = ['', '很简单', '简单', '中等', '困难', '很困难'];
    return difficultyNames[this.difficultyLevel] || '未知';
  }
}

// 定义模型
Question.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  questionNumber: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  questionType: {
    type: DataTypes.ENUM(
      'single_choice',
      'multiple_choice', 
      'true_false',
      'fill_blank',
      'short_answer',
      'essay',
      'calculation'
    ),
    allowNull: false
  },
  subject: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  difficultyLevel: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 3,
    validate: {
      min: 1,
      max: 5
    }
  },
  stem: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      len: [5, 10000]
    }
  },
  options: {
    type: DataTypes.JSON,
    allowNull: true
  },
  correctAnswer: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  explanation: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  sourceFileId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'file_uploads',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Question',
  tableName: 'questions',
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['questionType']
    },
    {
      fields: ['subject']
    },
    {
      fields: ['difficultyLevel']
    },
    {
      fields: ['sourceFileId']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// 定义关联关系
Question.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

User.hasMany(Question, {
  foreignKey: 'userId',
  as: 'questions'
});

export { Question };
export default Question;
