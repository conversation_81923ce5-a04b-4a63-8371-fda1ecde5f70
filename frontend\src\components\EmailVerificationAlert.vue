<template>
  <el-alert
    v-if="!authStore.user?.isEmailVerified && authStore.isAuthenticated"
    title="邮箱未验证"
    type="warning"
    :closable="false"
    show-icon
    class="email-verification-alert"
  >
    <template #default>
      <div class="alert-content">
        <p>您的邮箱尚未验证，部分功能可能受限。</p>
        <div class="alert-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click="resendVerification"
            :loading="resending"
          >
            {{ resending ? '发送中...' : '重新发送验证邮件' }}
          </el-button>
          <el-button 
            size="small" 
            @click="checkVerificationStatus"
            :loading="checking"
          >
            {{ checking ? '检查中...' : '我已验证' }}
          </el-button>
        </div>
      </div>
    </template>
  </el-alert>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'

const authStore = useAuthStore()
const resending = ref(false)
const checking = ref(false)

const resendVerification = async () => {
  if (!authStore.user?.email) {
    ElMessage.error('无法获取用户邮箱信息')
    return
  }
  
  resending.value = true
  try {
    await authApi.resendVerificationEmail(authStore.user.email)
    ElMessage.success('验证邮件已重新发送，请检查您的邮箱')
  } catch (error: any) {
    ElMessage.error(error.response?.data?.error || '发送失败，请稍后重试')
  } finally {
    resending.value = false
  }
}

const checkVerificationStatus = async () => {
  checking.value = true
  try {
    // 重新获取用户信息以检查验证状态
    await authStore.getCurrentUser()
    if (authStore.user?.isEmailVerified) {
      ElMessage.success('邮箱验证成功！')
    } else {
      ElMessage.info('邮箱尚未验证，请检查邮件并点击验证链接')
    }
  } catch (error: any) {
    ElMessage.error('检查验证状态失败')
  } finally {
    checking.value = false
  }
}
</script>

<style scoped>
.email-verification-alert {
  margin-bottom: 16px;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.alert-content p {
  margin: 0;
  flex: 1;
  min-width: 200px;
}

.alert-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .alert-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .alert-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
