# d2x - 智能试题解析平台

面向教育工作者的文档转换与智能试题解析SaaS平台，提供PDF转Word、试题解析、智能组卷等功能。

## 🚀 项目特色

- **PDF转Word转换**：支持基础版和高级版转换，高级版支持复杂公式和图表识别
- **智能试题解析**：自动从PDF/Word文档中提取题目、选项、答案和解析
- **题库管理**：支持题目分类、标签、难度等多维度管理
- **智能组卷**：从题库中选择题目，快速生成个性化试卷
- **邮箱验证系统**：用户注册需验证邮箱，确保账号安全性和真实性
- **用户权限管理**：免费用户、高级会员、管理员三级权限体系
- **任务中心**：实时查看文件处理和试卷生成进度

## 🛠 技术栈

### 后端
- **Node.js + TypeScript**：服务端开发
- **Express.js**：Web框架
- **MySQL + Sequelize**：数据库和ORM
- **Redis**：缓存和会话存储
- **JWT**：用户认证
- **Multer**：文件上传处理
- **Pandoc**：文档格式转换
- **Mathpix API**：高级公式识别（可选）

### 前端
- **Vue 3 + TypeScript**：前端框架
- **Vite**：构建工具
- **Element Plus**：UI组件库
- **Pinia**：状态管理
- **Vue Router**：路由管理
- **Axios**：HTTP客户端

## 📦 项目结构

```
datedu-hw/
├── backend/                 # 后端代码
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── services/       # 业务服务
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   └── index.ts        # 入口文件
│   ├── uploads/            # 文件上传目录
│   ├── temp/               # 临时文件目录
│   └── package.json
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── stores/         # 状态管理
│   │   ├── router/         # 路由配置
│   │   ├── api/            # API接口
│   │   ├── utils/          # 工具函数
│   │   └── main.ts         # 入口文件
│   └── package.json
└── README.md
```

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0（可选）
- Pandoc（用于文档转换）

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 环境配置

1. 复制环境变量文件：
```bash
# 后端
cd backend
cp .env.example .env

# 前端
cd ../frontend
cp .env.example .env.development
```

2. 设置MySQL数据库：
```bash
# 创建数据库和表结构
mysql -u root -p < database/init.sql
```

3. 修改 `backend/.env` 文件，配置MySQL连接信息：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=datedu_hw
DB_USER=root
DB_PASSWORD=your_mysql_password

# 邮件配置（邮箱验证必需）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
FRONTEND_URL=http://localhost:5173
```

4. 确保MySQL和Redis服务正在运行

5. 配置邮件服务（详见 `docs/EMAIL_SETUP.md`）

### 启动项目

#### 方式一：使用启动脚本（推荐）

```bash
# Linux/macOS
chmod +x start.sh
./start.sh

# Windows
start.bat
```

#### 方式二：手动启动

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端服务（新终端）
cd frontend
npm run dev
```

访问 http://localhost:5173 查看应用

#### 默认账号

系统会自动创建管理员账号：
- 用户名：admin
- 密码：admin123

您也可以直接注册新账号体验功能。

#### 邮件配置验证

```bash
# 检查邮件配置
npm run check-email

# 测试邮件发送
npm run test-email <EMAIL>
```

## 📚 主要功能

### 用户权限体系

- **免费用户**：每日限制使用次数
  - PDF转换：1次/天
  - 试题解析：1次/天
  - 试卷生成：1次/天

- **高级会员**：无限制使用
  - 支持高级PDF转换（Mathpix）
  - 无使用次数限制
  - 优先处理队列

- **管理员**：完整管理权限
  - 用户管理
  - 系统监控
  - 数据统计

### 核心工作流程

1. **文件上传**：支持PDF、Word格式，最大50MB
2. **任务创建**：创建后台处理任务
3. **异步处理**：使用队列系统处理文档
4. **结果通知**：处理完成后用户可下载结果

## 🔧 开发指南

### 后端开发

```bash
cd backend
npm run dev          # 开发模式
npm run build        # 构建项目
npm start           # 生产模式
npm run test        # 运行测试
```

### 前端开发

```bash
cd frontend
npm run dev         # 开发模式
npm run build       # 构建项目
npm run preview     # 预览构建结果
```

## 📄 许可证

本项目采用 MIT 许可证

## 🤝 贡献

欢迎提交 Issue 和 Pull Request

## 📞 联系

- 邮箱：<EMAIL>
- 项目地址：[GitHub](https://github.com/your-username/datedu-hw)
