#!/bin/bash

# d2x 项目启动脚本

echo "🚀 d2x 智能试题解析平台启动脚本"
echo "=================================="

# 检查Node.js版本
echo "📋 检查环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+ 版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，需要 16+ 版本，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js 版本: $(node -v)"

# 检查MySQL
if ! command -v mysql &> /dev/null; then
    echo "⚠️  MySQL 未安装或未在PATH中，请确保MySQL正在运行"
else
    echo "✅ MySQL 已安装"
fi

# 安装依赖
echo ""
echo "📦 安装依赖..."

echo "安装后端依赖..."
cd backend
if [ ! -d "node_modules" ]; then
    npm install
else
    echo "后端依赖已存在，跳过安装"
fi

echo "安装前端依赖..."
cd ../frontend
if [ ! -d "node_modules" ]; then
    npm install
else
    echo "前端依赖已存在，跳过安装"
fi

cd ..

# 检查环境配置
echo ""
echo "🔧 检查配置..."

if [ ! -f "backend/.env" ]; then
    echo "创建后端环境配置..."
    cp backend/.env.example backend/.env
    echo "✅ 已创建 backend/.env"
    echo "⚠️  请配置以下重要设置："
    echo "   - MySQL数据库连接信息"
    echo "   - SMTP邮件服务配置（用于邮箱验证）"
    echo "   - 详细配置说明请查看 docs/EMAIL_SETUP.md"
else
    echo "✅ 后端环境配置已存在"
fi

if [ ! -f "frontend/.env.development" ]; then
    echo "创建前端环境配置..."
    cp frontend/.env.example frontend/.env.development
    echo "✅ 已创建 frontend/.env.development"
else
    echo "✅ 前端环境配置已存在"
fi

# 启动服务
echo ""
echo "🚀 启动服务..."
echo "请在不同的终端窗口中运行以下命令："
echo ""
echo "1. 启动后端服务："
echo "   cd backend && npm run dev"
echo ""
echo "2. 启动前端服务："
echo "   cd frontend && npm run dev"
echo ""
echo "3. 访问应用："
echo "   http://localhost:5173"
echo ""
echo "📚 默认管理员账号："
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "📧 邮件配置检查："
echo "   运行 npm run check-email 检查邮件配置"
echo "   运行 npm run test-email 测试邮件发送"
echo ""
echo "📚 更多信息请查看 PROJECT_README.md"
echo ""

# 询问是否自动启动
read -p "是否自动启动后端服务？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "启动后端服务..."
    cd backend
    npm run dev &
    BACKEND_PID=$!
    echo "后端服务已启动 (PID: $BACKEND_PID)"
    
    echo "等待3秒后启动前端服务..."
    sleep 3
    
    cd ../frontend
    echo "启动前端服务..."
    npm run dev
fi
