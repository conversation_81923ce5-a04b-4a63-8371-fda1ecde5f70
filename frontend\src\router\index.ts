import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue')
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/Register.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/verify-email',
      name: 'VerifyEmail',
      component: () => import('@/views/VerifyEmail.vue')
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/Dashboard.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/pdf-convert',
      name: 'PdfConvert',
      component: () => import('@/views/PdfConvert.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/question-parse',
      name: 'QuestionParse',
      component: () => import('@/views/QuestionParse.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/question-bank',
      name: 'QuestionBank',
      component: () => import('@/views/QuestionBank.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/question/add',
      name: 'AddQuestion',
      component: () => import('@/views/QuestionEditor.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/question/edit/:id',
      name: 'EditQuestion',
      component: () => import('@/views/QuestionEditor.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/exam-builder',
      name: 'ExamBuilder',
      component: () => import('@/views/ExamBuilder.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/exam-list',
      name: 'ExamList',
      component: () => import('@/views/ExamList.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/Profile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tasks',
      name: 'Tasks',
      component: () => import('@/views/Tasks.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin/users',
      name: 'UserManagement',
      component: () => import('@/views/admin/UserManagement.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/404',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue')
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/404'
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 需要登录的页面
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // 需要管理员权限的页面
  if (to.meta.requiresAdmin && (!authStore.isAuthenticated || authStore.user?.userType !== 'admin')) {
    next('/dashboard')
    return
  }

  // 已登录用户访问登录/注册页面时跳转到dashboard
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  next()
})

export default router
