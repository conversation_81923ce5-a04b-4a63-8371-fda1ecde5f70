#!/usr/bin/env ts-node

/**
 * Pandoc功能测试脚本
 * 测试Word转HTML转换
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

async function testPandoc() {
  console.log('🧪 测试Pandoc功能...');
  console.log('================================');

  // 1. 检查Pandoc安装
  console.log('\n🔍 检查Pandoc安装...');
  try {
    const { stdout, stderr } = await execAsync('pandoc --version');
    console.log('✅ Pandoc已安装:');
    console.log(stdout.split('\n')[0]); // 只显示版本信息第一行
  } catch (error) {
    console.error('❌ Pandoc未安装或不在PATH中');
    console.log('💡 安装建议:');
    console.log('   Windows: 下载 https://pandoc.org/installing.html');
    console.log('   Linux: sudo apt-get install pandoc');
    console.log('   macOS: brew install pandoc');
    return;
  }

  // 2. 检查支持的格式
  console.log('\n📋 检查支持的输入格式...');
  try {
    const { stdout } = await execAsync('pandoc --list-input-formats');
    const formats = stdout.split('\n').filter(f => f.trim());
    const wordFormats = formats.filter(f => f.includes('docx') || f.includes('doc'));
    
    console.log('✅ 支持的Word格式:', wordFormats.join(', '));
    
    if (wordFormats.length === 0) {
      console.warn('⚠️ 未找到Word格式支持');
    }
  } catch (error) {
    console.error('❌ 无法获取支持格式:', error);
  }

  // 3. 创建测试Word文档内容
  console.log('\n📝 创建测试HTML内容...');
  const testHtmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试文档</title>
</head>
<body>
    <h1>测试题目</h1>
    
    <p><strong>1. 什么是HTML？</strong></p>
    <p>A. 超文本标记语言</p>
    <p>B. 编程语言</p>
    <p>C. 数据库</p>
    <p>D. 操作系统</p>
    
    <p><strong>2. CSS的作用是什么？</strong></p>
    <p>A. 控制网页样式</p>
    <p>B. 处理数据</p>
    <p>C. 编写逻辑</p>
    <p>D. 管理数据库</p>
    
    <p><strong>3. 请简述JavaScript的特点。</strong></p>
    <p>这是一道问答题，需要详细回答。</p>
</body>
</html>`;

  // 4. 测试HTML到Word转换（反向测试）
  const testDir = path.join(__dirname, '../../../temp');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  const htmlFile = path.join(testDir, 'test.html');
  const docxFile = path.join(testDir, 'test.docx');
  const convertedHtmlFile = path.join(testDir, 'converted.html');

  try {
    // 写入测试HTML文件
    fs.writeFileSync(htmlFile, testHtmlContent, 'utf-8');
    console.log('✅ 创建测试HTML文件:', htmlFile);

    // HTML转Word
    console.log('\n🔄 测试HTML转Word...');
    const htmlToDocxCommand = `pandoc "${htmlFile}" -o "${docxFile}"`;
    console.log('执行命令:', htmlToDocxCommand);
    
    await execAsync(htmlToDocxCommand);
    
    if (fs.existsSync(docxFile)) {
      const docxStats = fs.statSync(docxFile);
      console.log('✅ Word文件生成成功:', {
        path: docxFile,
        size: docxStats.size + ' bytes'
      });

      // Word转HTML（这是我们实际需要的功能）
      console.log('\n🔄 测试Word转HTML...');
      const docxToHtmlCommand = `pandoc "${docxFile}" -t html -o "${convertedHtmlFile}"`;
      console.log('执行命令:', docxToHtmlCommand);
      
      await execAsync(docxToHtmlCommand);
      
      if (fs.existsSync(convertedHtmlFile)) {
        const htmlContent = fs.readFileSync(convertedHtmlFile, 'utf-8');
        console.log('✅ HTML转换成功:', {
          path: convertedHtmlFile,
          contentLength: htmlContent.length,
          preview: htmlContent.substring(0, 200) + '...'
        });

        // 分析转换质量
        const hasTitle = htmlContent.includes('测试题目');
        const hasQuestions = htmlContent.includes('什么是HTML');
        const hasOptions = htmlContent.includes('超文本标记语言');
        
        console.log('\n📊 转换质量分析:');
        console.log('  标题保留:', hasTitle ? '✅' : '❌');
        console.log('  题目保留:', hasQuestions ? '✅' : '❌');
        console.log('  选项保留:', hasOptions ? '✅' : '❌');
        
      } else {
        console.error('❌ Word转HTML失败，未生成输出文件');
      }
      
    } else {
      console.error('❌ HTML转Word失败，未生成Word文件');
    }

  } catch (error: any) {
    console.error('❌ 转换测试失败:', error.message);
    
    // 尝试简化命令
    console.log('\n🔄 尝试简化命令...');
    try {
      const simpleCommand = `pandoc "${htmlFile}" -t docx`;
      const { stdout } = await execAsync(simpleCommand);
      console.log('✅ 简化命令输出长度:', stdout.length);
    } catch (simpleError) {
      console.error('❌ 简化命令也失败:', simpleError);
    }
  }

  // 5. 清理测试文件
  console.log('\n🗑️ 清理测试文件...');
  try {
    [htmlFile, docxFile, convertedHtmlFile].forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log('删除:', path.basename(file));
      }
    });
  } catch (error) {
    console.warn('⚠️ 清理文件时出现警告:', error);
  }

  console.log('\n🎯 测试总结:');
  console.log('- Pandoc安装状态');
  console.log('- Word格式支持');
  console.log('- 转换功能测试');
  console.log('- 转换质量评估');
}

// 运行测试
testPandoc().catch(console.error);
