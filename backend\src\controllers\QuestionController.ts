import { Response } from 'express';
import { Op } from 'sequelize';
import { Question } from '../models/Question';
import { AuthRequest } from '../middleware/auth';

export class QuestionController {
  // 获取题库列表
  async getQuestions(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const {
        page = 1,
        limit = 20,
        keyword,
        questionType,
        subject,
        difficultyLevel,
        tags,
        sourceFileId
      } = req.query;
      
      const whereConditions: any = { userId: user.id };

      // 关键词搜索
      if (keyword) {
        whereConditions[Op.or] = [
          { stem: { [Op.like]: `%${keyword}%` } },
          { correctAnswer: { [Op.like]: `%${keyword}%` } },
          { explanation: { [Op.like]: `%${keyword}%` } }
        ];
      }

      // 题目类型筛选
      if (questionType) {
        whereConditions.questionType = questionType;
      }

      // 学科筛选
      if (subject) {
        whereConditions.subject = subject;
      }

      // 难度筛选
      if (difficultyLevel) {
        whereConditions.difficultyLevel = Number(difficultyLevel);
      }

      // 标签筛选 - 暂时跳过，在查询后过滤
      let tagFilter: string[] | null = null;
      if (tags) {
        tagFilter = Array.isArray(tags) ? tags as string[] : [tags as string];
      }
      
      // 来源文件筛选
      if (sourceFileId) {
        whereConditions.sourceFileId = sourceFileId;
      }

      const skip = (Number(page) - 1) * Number(limit);

      let questions: any[];
      let total: number;

      if (tagFilter) {
        // 如果有标签过滤，先获取所有符合其他条件的题目，然后在内存中过滤
        const allQuestions = await Question.findAll({
          where: whereConditions,
          order: [['createdAt', 'DESC']]
        });

        const filteredQuestions = allQuestions.filter((question: any) => {
          const questionTags = question.tags || [];
          return tagFilter!.some(tag => questionTags.includes(tag));
        });

        total = filteredQuestions.length;
        questions = filteredQuestions.slice(skip, skip + Number(limit));
      } else {
        // 没有标签过滤，直接查询
        [questions, total] = await Promise.all([
          Question.findAll({
            where: whereConditions,
            order: [['createdAt', 'DESC']],
            offset: skip,
            limit: Number(limit)
          }),
          Question.count({ where: whereConditions })
        ]);
      }

      res.json({
        questions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get questions error:', error);
      res.status(500).json({ error: '获取题目列表失败' });
    }
  }
  
  // 获取题目详情
  async getQuestionById(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { questionId } = req.params;
      
      const question = await Question.findOne({
        where: {
          id: questionId,
          userId: user.id
        }
      });
      
      if (!question) {
        res.status(404).json({ error: '题目不存在' });
        return;
      }
      
      res.json({ question });
    } catch (error) {
      console.error('Get question by id error:', error);
      res.status(500).json({ error: '获取题目详情失败' });
    }
  }
  
  // 创建题目
  async createQuestion(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const questionData = req.body;
      
      const question = await Question.create({
        ...questionData,
        userId: user.id
      });
      
      res.status(201).json({
        message: '题目创建成功',
        question
      });
    } catch (error) {
      console.error('Create question error:', error);
      res.status(500).json({ error: '创建题目失败' });
    }
  }
  
  // 更新题目
  async updateQuestion(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { questionId } = req.params;
      const updateData = req.body;
      
      const [affectedCount] = await Question.update(updateData, {
        where: { id: questionId, userId: user.id },
        returning: true
      });

      if (affectedCount === 0) {
        res.status(404).json({ error: '题目不存在或无权限修改' });
        return;
      }

      const question = await Question.findByPk(questionId);
      
      if (!question) {
        res.status(404).json({ error: '题目不存在' });
        return;
      }
      
      res.json({
        message: '题目更新成功',
        question
      });
    } catch (error) {
      console.error('Update question error:', error);
      res.status(500).json({ error: '更新题目失败' });
    }
  }
  
  // 删除题目
  async deleteQuestion(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { questionId } = req.params;
      
      const question = await Question.findOne({
        where: {
          id: questionId,
          userId: user.id
        }
      });

      if (!question) {
        res.status(404).json({ error: '题目不存在' });
        return;
      }

      await question.destroy();

      res.json({ message: '题目删除成功' });
    } catch (error) {
      console.error('Delete question error:', error);
      res.status(500).json({ error: '删除题目失败' });
    }
  }
  
  // 批量删除题目
  async batchDeleteQuestions(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { questionIds } = req.body;
      
      if (!Array.isArray(questionIds) || questionIds.length === 0) {
        res.status(400).json({ error: '请提供要删除的题目ID列表' });
        return;
      }
      
      const deletedCount = await Question.destroy({
        where: {
          id: { [Op.in]: questionIds },
          userId: user.id
        }
      });

      res.json({
        message: `成功删除${deletedCount}道题目`
      });
    } catch (error) {
      console.error('Batch delete questions error:', error);
      res.status(500).json({ error: '批量删除题目失败' });
    }
  }
  
  // 搜索题目
  async searchQuestions(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { q, limit = 10 } = req.query;
      
      if (!q) {
        res.status(400).json({ error: '请提供搜索关键词' });
        return;
      }
      
      const questions = await Question.findAll({
        where: {
          userId: user.id,
          [Op.or]: [
            { stem: { [Op.like]: `%${q}%` } },
            { correctAnswer: { [Op.like]: `%${q}%` } },
            { explanation: { [Op.like]: `%${q}%` } }
          ]
        },
        limit: Number(limit),
        order: [['createdAt', 'DESC']]
      });
      
      res.json({ questions });
    } catch (error) {
      console.error('Search questions error:', error);
      res.status(500).json({ error: '搜索题目失败' });
    }
  }
  
  // 按标签获取题目
  async getQuestionsByTag(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { tag } = req.params;
      const { page = 1, limit = 20 } = req.query;
      
      const skip = (Number(page) - 1) * Number(limit);
      
      // 先获取所有用户的题目，然后在内存中过滤
      const allQuestions = await Question.findAll({
        where: {
          userId: user.id
        },
        order: [['createdAt', 'DESC']]
      });

      // 在内存中过滤包含指定标签的题目
      const filteredQuestions = allQuestions.filter((question: any) => {
        const tags = question.tags || [];
        return tags.includes(tag);
      });

      const total = filteredQuestions.length;
      const questions = filteredQuestions.slice(skip, skip + Number(limit));
      
      res.json({
        questions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(Number(total) / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get questions by tag error:', error);
      res.status(500).json({ error: '获取标签题目失败' });
    }
  }
  
  // 按学科获取题目
  async getQuestionsBySubject(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { subject } = req.params;
      const { page = 1, limit = 20 } = req.query;
      
      const skip = (Number(page) - 1) * Number(limit);
      
      const [questions, total] = await Promise.all([
        Question.findAll({
          where: {
            userId: user.id,
            subject
          },
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit)
        }),
        Question.count({
          where: {
            userId: user.id,
            subject
          }
        })
      ]);
      
      res.json({
        questions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get questions by subject error:', error);
      res.status(500).json({ error: '获取学科题目失败' });
    }
  }
  
  // 按难度获取题目
  async getQuestionsByDifficulty(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { level } = req.params;
      const { page = 1, limit = 20 } = req.query;
      
      const difficultyLevel = Number(level);
      if (difficultyLevel < 1 || difficultyLevel > 5) {
        res.status(400).json({ error: '难度等级必须在1-5之间' });
        return;
      }
      
      const skip = (Number(page) - 1) * Number(limit);
      
      const [questions, total] = await Promise.all([
        Question.findAll({
          where: {
            userId: user.id,
            difficultyLevel
          },
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit)
        }),
        Question.count({
          where: {
            userId: user.id,
            difficultyLevel
          }
        })
      ]);
      
      res.json({
        questions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get questions by difficulty error:', error);
      res.status(500).json({ error: '获取难度题目失败' });
    }
  }
  
  // 获取题目统计信息
  async getQuestionStats(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      
      // 获取总数
      const totalCount = await Question.count({ where: { userId: user.id } });

      // 获取所有题目用于统计
      const allQuestions = await Question.findAll({
        where: { userId: user.id },
        attributes: ['questionType', 'subject', 'difficultyLevel', 'tags']
      });

      // 统计题目类型
      const typeStats = allQuestions.reduce((acc: any, question: any) => {
        const type = question.questionType;
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});

      // 统计学科
      const subjectStats = allQuestions.reduce((acc: any, question: any) => {
        if (question.subject) {
          acc[question.subject] = (acc[question.subject] || 0) + 1;
        }
        return acc;
      }, {});

      // 统计难度
      const difficultyStats = allQuestions.reduce((acc: any, question: any) => {
        const level = question.difficultyLevel;
        acc[level] = (acc[level] || 0) + 1;
        return acc;
      }, {});

      // 统计标签
      const tagCounts: any = {};
      allQuestions.forEach((question: any) => {
        if (question.tags && Array.isArray(question.tags)) {
          question.tags.forEach((tag: string) => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          });
        }
      });

      // 取前10个最常用标签
      const tagStats = Object.entries(tagCounts)
        .sort(([,a], [,b]) => (b as number) - (a as number))
        .slice(0, 10)
        .map(([tag, count]) => ({ _id: tag, count }));
      
      res.json({
        stats: {
          totalCount,
          typeStats,
          subjectStats,
          difficultyStats,
          tagStats
        }
      });
    } catch (error) {
      console.error('Get question stats error:', error);
      res.status(500).json({ error: '获取题目统计失败' });
    }
  }
}
