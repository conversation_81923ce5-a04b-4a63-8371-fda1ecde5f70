import { Router } from 'express';
import { UserController } from '../controllers/UserController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();
const userController = new UserController();

// 获取用户资料
router.get('/profile', authenticateToken, userController.getProfile.bind(userController));

// 更新用户资料
router.put('/profile', authenticateToken, userController.updateProfile.bind(userController));

// 上传头像
router.post('/avatar', authenticateToken, userController.uploadAvatar.bind(userController));

// 获取用户统计信息
router.get('/stats', authenticateToken, userController.getUserStats.bind(userController));

// 获取用户使用记录
router.get('/usage-history', authenticateToken, userController.getUsageHistory.bind(userController));

// 管理员路由
router.get('/all', authenticateToken, requireAdmin, userController.getAllUsers.bind(userController));
router.put('/:userId/status', authenticateToken, requireAdmin, userController.updateUserStatus.bind(userController));
router.put('/:userId/type', authenticateToken, requireAdmin, userController.updateUserType.bind(userController));

export default router;
