import { Response } from 'express';
import { User } from '../models/User';
import { Question } from '../models/Question';
import { Exam } from '../models/Exam';
import { FileUpload } from '../models/FileUpload';
import { ProcessingTask } from '../models/ProcessingTask';
import { AuthRequest } from '../middleware/auth';

export class UserController {
  // 获取用户资料
  async getProfile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      
      res.json({
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          avatarUrl: user.avatarUrl,
          userType: user.userType,
          subscriptionExpiresAt: user.subscriptionExpiresAt,
          dailyUsage: user.dailyUsage,
          lastUsageReset: user.lastUsageReset,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({ error: '获取用户资料失败' });
    }
  }
  
  // 更新用户资料
  async updateProfile(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { fullName, avatarUrl } = req.body;
      
      if (fullName !== undefined) user.fullName = fullName;
      if (avatarUrl !== undefined) user.avatarUrl = avatarUrl;
      
      await user.save();
      
      res.json({
        message: '用户资料更新成功',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          avatarUrl: user.avatarUrl,
          userType: user.userType
        }
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({ error: '更新用户资料失败' });
    }
  }
  
  // 上传头像
  async uploadAvatar(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const file = req.file;
      
      if (!file) {
        res.status(400).json({ error: '请选择头像文件' });
        return;
      }
      
      // 这里应该将文件上传到云存储，暂时使用本地路径
      const avatarUrl = `/uploads/${file.filename}`;
      user.avatarUrl = avatarUrl;
      await user.save();
      
      res.json({
        message: '头像上传成功',
        avatarUrl
      });
    } catch (error) {
      console.error('Upload avatar error:', error);
      res.status(500).json({ error: '头像上传失败' });
    }
  }
  
  // 获取用户统计信息
  async getUserStats(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      
      const [questionsCount, examsCount, filesCount, tasksCount] = await Promise.all([
        Question.count({ where: { userId: user.id } }),
        Exam.count({ where: { userId: user.id } }),
        FileUpload.count({ where: { userId: user.id } }),
        ProcessingTask.count({ where: { userId: user.id } })
      ]);
      
      res.json({
        stats: {
          questionsCount,
          examsCount,
          filesCount,
          tasksCount
        }
      });
    } catch (error) {
      console.error('Get user stats error:', error);
      res.status(500).json({ error: '获取统计信息失败' });
    }
  }
  
  // 获取用户使用记录
  async getUsageHistory(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { page = 1, limit = 20 } = req.query;
      
      const skip = (Number(page) - 1) * Number(limit);
      
      const [tasks, total] = await Promise.all([
        ProcessingTask.findAll({
          where: { userId: user.id },
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit),
          include: ['fileUpload']
        }),
        ProcessingTask.count({ where: { userId: user.id } })
      ]);
      
      res.json({
        tasks,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get usage history error:', error);
      res.status(500).json({ error: '获取使用记录失败' });
    }
  }
  
  // 获取所有用户（管理员）
  async getAllUsers(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20, userType, isActive, keyword } = req.query;

      const query: any = {};
      if (userType) query.userType = userType;
      if (isActive !== undefined) query.isActive = isActive === 'true';

      // 关键词搜索
      if (keyword) {
        const { Op } = require('sequelize');
        query[Op.or] = [
          { username: { [Op.like]: `%${keyword}%` } },
          { email: { [Op.like]: `%${keyword}%` } },
          { fullName: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const skip = (Number(page) - 1) * Number(limit);

      const [users, total] = await Promise.all([
        User.findAll({
          where: query,
          attributes: { exclude: ['passwordHash'] },
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit)
        }),
        User.count({ where: query })
      ]);

      res.json({
        users,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get all users error:', error);
      res.status(500).json({ error: '获取用户列表失败' });
    }
  }
  
  // 更新用户状态（管理员）
  async updateUserStatus(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { isActive } = req.body;
      
      const user = await User.findByPk(userId);
      if (!user) {
        res.status(404).json({ error: '用户不存在' });
        return;
      }
      
      user.isActive = isActive;
      await user.save();
      
      res.json({
        message: '用户状态更新成功',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          isActive: user.isActive
        }
      });
    } catch (error) {
      console.error('Update user status error:', error);
      res.status(500).json({ error: '更新用户状态失败' });
    }
  }
  
  // 更新用户类型（管理员）
  async updateUserType(req: AuthRequest, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      const { userType } = req.body;
      
      if (!['free', 'premium', 'admin'].includes(userType)) {
        res.status(400).json({ error: '无效的用户类型' });
        return;
      }
      
      const user = await User.findByPk(userId);
      if (!user) {
        res.status(404).json({ error: '用户不存在' });
        return;
      }
      
      user.userType = userType;
      
      // 如果升级为高级会员，设置过期时间
      if (userType === 'premium') {
        const expiresAt = new Date();
        expiresAt.setFullYear(expiresAt.getFullYear() + 1); // 1年后过期
        user.subscriptionExpiresAt = expiresAt;
      }
      
      await user.save();
      
      res.json({
        message: '用户类型更新成功',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          userType: user.userType,
          subscriptionExpiresAt: user.subscriptionExpiresAt
        }
      });
    } catch (error) {
      console.error('Update user type error:', error);
      res.status(500).json({ error: '更新用户类型失败' });
    }
  }
}
