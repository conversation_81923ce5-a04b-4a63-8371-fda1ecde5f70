<template>
  <div class="question-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="题目编号" prop="questionNumber">
            <el-input v-model="form.questionNumber" placeholder="如：1、2、3..." />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="题目类型" prop="questionType">
            <el-select v-model="form.questionType" placeholder="请选择题目类型" @change="onTypeChange">
              <el-option label="单选题" value="single_choice" />
              <el-option label="多选题" value="multiple_choice" />
              <el-option label="判断题" value="true_false" />
              <el-option label="填空题" value="fill_blank" />
              <el-option label="简答题" value="short_answer" />
              <el-option label="论述题" value="essay" />
              <el-option label="计算题" value="calculation" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="学科" prop="subject">
            <el-input v-model="form.subject" placeholder="如：数学、语文、英语..." />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="难度等级" prop="difficultyLevel">
            <el-rate
              v-model="form.difficultyLevel"
              :max="5"
              show-text
              :texts="['很简单', '简单', '中等', '困难', '很困难']"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="题干" prop="stem">
        <el-input
          v-model="form.stem"
          type="textarea"
          :rows="4"
          placeholder="请输入题目内容..."
        />
      </el-form-item>

      <!-- 选择题选项 -->
      <div v-if="showOptions" class="options-section">
        <el-form-item label="选项">
          <div class="options-list">
            <div
              v-for="(option, index) in form.options"
              :key="index"
              class="option-item"
            >
              <div class="option-header">
                <span class="option-label">{{ option.key }}.</span>
                <el-checkbox
                  v-if="form.questionType === 'multiple_choice'"
                  v-model="option.isCorrect"
                  @change="updateCorrectAnswer"
                >
                  正确答案
                </el-checkbox>
                <el-radio
                  v-else-if="form.questionType === 'single_choice'"
                  v-model="correctOption"
                  :label="option.key"
                  @change="updateCorrectAnswer"
                >
                  正确答案
                </el-radio>
              </div>
              <el-input
                v-model="option.content"
                placeholder="请输入选项内容..."
                class="option-input"
              />
              <el-button
                v-if="form.options.length > 2"
                type="danger"
                size="small"
                @click="removeOption(index)"
              >
                删除
              </el-button>
            </div>
            
            <el-button
              v-if="form.options.length < 8"
              type="primary"
              plain
              @click="addOption"
            >
              <el-icon><Plus /></el-icon>
              添加选项
            </el-button>
          </div>
        </el-form-item>
      </div>

      <!-- 非选择题答案 -->
      <el-form-item
        v-if="!showOptions"
        label="参考答案"
        prop="correctAnswer"
      >
        <el-input
          v-model="form.correctAnswer"
          type="textarea"
          :rows="3"
          placeholder="请输入参考答案..."
        />
      </el-form-item>

      <el-form-item label="解析" prop="explanation">
        <el-input
          v-model="form.explanation"
          type="textarea"
          :rows="3"
          placeholder="请输入题目解析（可选）..."
        />
      </el-form-item>

      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          placeholder="请选择或输入标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="saving">
          {{ question ? '更新题目' : '创建题目' }}
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

interface Option {
  key: string
  content: string
  isCorrect?: boolean
}

interface QuestionForm {
  questionNumber: string
  questionType: string
  subject: string
  difficultyLevel: number
  stem: string
  options: Option[]
  correctAnswer: string
  explanation: string
  tags: string[]
}

const props = defineProps<{
  question?: any
}>()

const emit = defineEmits<{
  save: [data: any]
  cancel: []
}>()

const formRef = ref<FormInstance>()
const saving = ref(false)
const correctOption = ref('')

const form = reactive<QuestionForm>({
  questionNumber: '',
  questionType: 'single_choice',
  subject: '',
  difficultyLevel: 3,
  stem: '',
  options: [
    { key: 'A', content: '' },
    { key: 'B', content: '' },
    { key: 'C', content: '' },
    { key: 'D', content: '' }
  ],
  correctAnswer: '',
  explanation: '',
  tags: []
})

const commonTags = ref([
  '基础', '应用', '综合', '计算', '理论',
  '重点', '难点', '常考', '易错', '拓展'
])

const rules: FormRules = {
  questionType: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  stem: [
    { required: true, message: '请输入题干', trigger: 'blur' },
    { min: 5, message: '题干长度至少5个字符', trigger: 'blur' }
  ]
}

const showOptions = computed(() => {
  return ['single_choice', 'multiple_choice', 'true_false'].includes(form.questionType)
})

const onTypeChange = (type: string) => {
  if (type === 'true_false') {
    form.options = [
      { key: 'A', content: '正确', isCorrect: false },
      { key: 'B', content: '错误', isCorrect: false }
    ]
  } else if (type === 'single_choice' || type === 'multiple_choice') {
    if (form.options.length < 2) {
      form.options = [
        { key: 'A', content: '' },
        { key: 'B', content: '' },
        { key: 'C', content: '' },
        { key: 'D', content: '' }
      ]
    }
  }
  
  // 清空答案
  form.correctAnswer = ''
  correctOption.value = ''
  form.options.forEach(option => {
    option.isCorrect = false
  })
}

const addOption = () => {
  const nextKey = String.fromCharCode(65 + form.options.length) // A, B, C, D...
  form.options.push({
    key: nextKey,
    content: ''
  })
}

const removeOption = (index: number) => {
  form.options.splice(index, 1)
  
  // 重新分配选项键
  form.options.forEach((option, i) => {
    option.key = String.fromCharCode(65 + i)
  })
  
  updateCorrectAnswer()
}

const updateCorrectAnswer = () => {
  if (form.questionType === 'single_choice') {
    form.correctAnswer = correctOption.value
  } else if (form.questionType === 'multiple_choice') {
    const correctOptions = form.options
      .filter(option => option.isCorrect)
      .map(option => option.key)
    form.correctAnswer = correctOptions.join(',')
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 验证选择题选项
    if (showOptions.value) {
      const hasEmptyOption = form.options.some(option => !option.content.trim())
      if (hasEmptyOption) {
        ElMessage.error('请填写所有选项内容')
        return
      }
      
      if (form.questionType === 'single_choice' && !form.correctAnswer) {
        ElMessage.error('请选择正确答案')
        return
      }
      
      if (form.questionType === 'multiple_choice') {
        const hasCorrect = form.options.some(option => option.isCorrect)
        if (!hasCorrect) {
          ElMessage.error('请至少选择一个正确答案')
          return
        }
      }
    }
    
    saving.value = true
    
    const submitData = {
      ...form,
      options: showOptions.value ? form.options : undefined
    }
    
    emit('save', submitData)
  } catch (error) {
    console.error('Form validation failed:', error)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 初始化表单数据
const initForm = () => {
  if (props.question) {
    Object.assign(form, {
      questionNumber: props.question.questionNumber || '',
      questionType: props.question.questionType || 'single_choice',
      subject: props.question.subject || '',
      difficultyLevel: props.question.difficultyLevel || 3,
      stem: props.question.stem || '',
      options: props.question.options || [
        { key: 'A', content: '' },
        { key: 'B', content: '' },
        { key: 'C', content: '' },
        { key: 'D', content: '' }
      ],
      correctAnswer: props.question.correctAnswer || '',
      explanation: props.question.explanation || '',
      tags: props.question.tags || []
    })
    
    // 设置单选题的正确答案
    if (form.questionType === 'single_choice' && form.correctAnswer) {
      correctOption.value = form.correctAnswer
    }
  }
}

watch(() => props.question, initForm, { immediate: true })

onMounted(() => {
  initForm()
})
</script>

<style scoped>
.question-form {
  max-width: 800px;
}

.options-section {
  margin: 20px 0;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 12px;
  background: #fafafa;
}

.option-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.option-label {
  font-weight: 600;
  color: #409eff;
  min-width: 20px;
}

.option-input {
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .option-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
