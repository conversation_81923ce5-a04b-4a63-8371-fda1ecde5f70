import { Response } from 'express';
import { Op } from 'sequelize';
import { Exam } from '../models/Exam';
import { Question } from '../models/Question';
import { ProcessingTask } from '../models/ProcessingTask';
import { AuthRequest } from '../middleware/auth';
import { ExamGenerator } from '../services/ExamGenerator';

export class ExamController {
  private examGenerator = new ExamGenerator();
  
  // 获取试卷列表
  async getExams(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { page = 1, limit = 20, subject, examType } = req.query;
      
      const whereConditions: any = { userId: user.id };

      if (subject) whereConditions.subject = subject;
      if (examType) whereConditions.examType = examType;

      const skip = (Number(page) - 1) * Number(limit);

      const [exams, total] = await Promise.all([
        Exam.findAll({
          where: whereConditions,
          order: [['createdAt', 'DESC']],
          offset: skip,
          limit: Number(limit)
        }),
        Exam.count({ where: whereConditions })
      ]);
      
      res.json({
        exams,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error('Get exams error:', error);
      res.status(500).json({ error: '获取试卷列表失败' });
    }
  }
  
  // 获取试卷详情
  async getExamById(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      
      const exam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });
      
      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }
      
      res.json({ exam });
    } catch (error) {
      console.error('Get exam by id error:', error);
      res.status(500).json({ error: '获取试卷详情失败' });
    }
  }
  
  // 创建试卷
  async createExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const examData = req.body;
      
      const exam = await Exam.create({
        ...examData,
        userId: user.id,
        questions: [],
        isPublished: false
      });
      
      res.status(201).json({
        message: '试卷创建成功',
        exam
      });
    } catch (error) {
      console.error('Create exam error:', error);
      res.status(500).json({ error: '创建试卷失败' });
    }
  }
  
  // 更新试卷
  async updateExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      const updateData = req.body;
      
      const [affectedCount] = await Exam.update(updateData, {
        where: { id: examId, userId: user.id },
        returning: true
      });

      if (affectedCount === 0) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }

      const exam = await Exam.findByPk(examId);
      
      res.json({
        message: '试卷更新成功',
        exam
      });
    } catch (error) {
      console.error('Update exam error:', error);
      res.status(500).json({ error: '更新试卷失败' });
    }
  }
  
  // 删除试卷
  async deleteExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      
      const exam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });

      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }

      await exam.destroy();
      
      res.json({ message: '试卷删除成功' });
    } catch (error) {
      console.error('Delete exam error:', error);
      res.status(500).json({ error: '删除试卷失败' });
    }
  }
  
  // 向试卷添加题目
  async addQuestionToExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      const { questionId, sectionTitle, score = 1 } = req.body;
      
      const [exam, question] = await Promise.all([
        Exam.findOne({
          where: { id: examId, userId: user.id }
        }),
        Question.findOne({
          where: { id: questionId, userId: user.id }
        })
      ]);
      
      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }
      
      if (!question) {
        res.status(404).json({ error: '题目不存在' });
        return;
      }
      
      // 检查题目是否已存在
      const existingQuestion = exam.questions.find((q: any) =>
        q.questionId.toString() === questionId
      );
      
      if (existingQuestion) {
        res.status(400).json({ error: '题目已存在于试卷中' });
        return;
      }
      
      exam.addQuestion(questionId, sectionTitle, score);
      await exam.save();
      
      res.json({
        message: '题目添加成功',
        exam
      });
    } catch (error) {
      console.error('Add question to exam error:', error);
      res.status(500).json({ error: '添加题目失败' });
    }
  }
  
  // 从试卷移除题目
  async removeQuestionFromExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId, questionId } = req.params;
      
      const exam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });
      
      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }
      
      exam.removeQuestion(parseInt(questionId));
      await exam.save();
      
      res.json({
        message: '题目移除成功',
        exam
      });
    } catch (error) {
      console.error('Remove question from exam error:', error);
      res.status(500).json({ error: '移除题目失败' });
    }
  }
  
  // 调整试卷题目顺序
  async reorderExamQuestions(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      const { questionIds } = req.body;
      
      if (!Array.isArray(questionIds)) {
        res.status(400).json({ error: '题目ID列表格式错误' });
        return;
      }
      
      const exam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });
      
      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }
      
      exam.reorderQuestions(questionIds);
      await exam.save();
      
      res.json({
        message: '题目顺序调整成功',
        exam
      });
    } catch (error) {
      console.error('Reorder exam questions error:', error);
      res.status(500).json({ error: '调整题目顺序失败' });
    }
  }
  
  // 生成试卷Word文档
  async generateExamDocument(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      
      const exam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });
      
      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }
      
      if (exam.questions.length === 0) {
        res.status(400).json({ error: '试卷中没有题目' });
        return;
      }
      
      // 创建生成任务
      const task = await ProcessingTask.create({
        userId: user.id,
        taskType: 'exam_generation',
        status: 'pending',
        progress: 0
      });

      // 增加用户使用次数
      user.incrementUsage('examGenerate');
      await user.save();

      // 异步生成试卷文档
      this.examGenerator.generateExamDocument(task.id.toString(), exam)
        .catch(error => {
          console.error('试卷生成处理失败:', error);
        });

      res.json({
        message: '试卷生成任务已创建',
        taskId: task.id
      });
    } catch (error) {
      console.error('Generate exam document error:', error);
      res.status(500).json({ error: '生成试卷文档失败' });
    }
  }
  
  // 预览试卷
  async previewExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      
      const exam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });
      
      if (!exam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }
      
      // 生成HTML预览
      const htmlContent = await this.examGenerator.generateExamHTML(exam);
      
      res.json({
        exam,
        htmlContent
      });
    } catch (error) {
      console.error('Preview exam error:', error);
      res.status(500).json({ error: '预览试卷失败' });
    }
  }
  
  // 复制试卷
  async duplicateExam(req: AuthRequest, res: Response): Promise<void> {
    try {
      const user = req.user!;
      const { examId } = req.params;
      
      const originalExam = await Exam.findOne({
        where: {
          id: examId,
          userId: user.id
        }
      });

      if (!originalExam) {
        res.status(404).json({ error: '试卷不存在' });
        return;
      }

      const duplicatedExam = await Exam.create({
        userId: user.id,
        title: `${originalExam.title} (副本)`,
        description: originalExam.description,
        subject: originalExam.subject,
        examType: originalExam.examType,
        totalScore: originalExam.totalScore,
        timeLimit: originalExam.timeLimit,
        instructions: originalExam.instructions,
        questions: originalExam.questions,
        isPublished: false
      });
      
      res.status(201).json({
        message: '试卷复制成功',
        exam: duplicatedExam
      });
    } catch (error) {
      console.error('Duplicate exam error:', error);
      res.status(500).json({ error: '复制试卷失败' });
    }
  }
}
