<template>
  <div class="home">
    <!-- 导航栏 -->
    <header class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <h1 class="logo">d2x</h1>
          <span class="tagline">智能试题解析平台</span>
        </div>
        <nav class="nav-menu">
          <a href="#features" class="nav-link">功能特色</a>
          <a href="#pricing" class="nav-link">价格方案</a>
          <a href="#about" class="nav-link">关于我们</a>
          <div class="nav-actions">
            <template v-if="authStore.isAuthenticated">
              <el-button @click="$router.push('/dashboard')">进入工作台</el-button>
              <el-button @click="handleLogout">退出登录</el-button>
            </template>
            <template v-else>
              <el-button @click="$router.push('/login')">登录</el-button>
              <el-button type="primary" @click="$router.push('/register')">免费注册</el-button>
            </template>
          </div>
        </nav>

        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <el-icon><Menu /></el-icon>
        </div>
      </div>
    </header>

    <!-- 英雄区域 -->
    <section class="hero">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            让教学更智能
            <br>
            <span class="highlight">试题解析从未如此简单</span>
          </h1>
          <p class="hero-description">
            d2x是面向教育工作者的智能试题解析平台，支持PDF转Word、试题自动解析、智能组卷等功能，
            让您从繁琐的试卷录入工作中解放出来，专注于教学本身。
          </p>
          <div class="hero-actions">
            <el-button type="primary" size="large" @click="$router.push('/register')">
              <el-icon><Star /></el-icon>
              免费开始使用
            </el-button>
            <el-button size="large" @click="scrollToDemo">
              <el-icon><VideoPlay /></el-icon>
              观看演示
            </el-button>
          </div>
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">10,000+</div>
              <div class="stat-label">教师用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">500万+</div>
              <div class="stat-label">题目解析</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">识别准确率</div>
            </div>
          </div>
        </div>
        <div class="hero-image">
          <div class="demo-card">
            <div class="demo-header">
              <div class="demo-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <span class="demo-title">d2x 工作台</span>
            </div>
            <div class="demo-content">
              <div class="demo-upload">
                <el-icon size="48" color="#409eff"><UploadFilled /></el-icon>
                <p>拖拽PDF文件到此处</p>
              </div>
              <div class="demo-progress">
                <el-progress :percentage="75" status="success" />
                <p>正在解析试题...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特色 -->
    <section id="features" class="features">
      <div class="container">
        <div class="section-header">
          <h2>强大功能，简单易用</h2>
          <p>一站式解决教育工作者的文档处理需求</p>
        </div>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon pdf-icon">
              <el-icon size="32"><Document /></el-icon>
            </div>
            <h3>PDF转Word</h3>
            <p>支持基础版和高级版转换，高级版可识别复杂公式和图表，保持原有格式</p>
            <ul class="feature-list">
              <li>支持数学公式识别</li>
              <li>保持原有排版格式</li>
              <li>批量处理多个文件</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon parse-icon">
              <el-icon size="32"><EditPen /></el-icon>
            </div>
            <h3>智能试题解析</h3>
            <p>自动识别题号、题干、选项和答案，支持多种题型，准确率高达99.9%</p>
            <ul class="feature-list">
              <li>支持单选、多选、判断题</li>
              <li>自动提取答案和解析</li>
              <li>智能分类和标签</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon bank-icon">
              <el-icon size="32"><Collection /></el-icon>
            </div>
            <h3>题库管理</h3>
            <p>强大的题库管理系统，支持分类、搜索、标签等多维度管理</p>
            <ul class="feature-list">
              <li>多维度筛选搜索</li>
              <li>题目分类管理</li>
              <li>批量操作支持</li>
            </ul>
          </div>

          <div class="feature-card">
            <div class="feature-icon exam-icon">
              <el-icon size="32"><Notebook /></el-icon>
            </div>
            <h3>智能组卷</h3>
            <p>从题库中快速选择题目，生成个性化试卷，支持Word格式导出</p>
            <ul class="feature-list">
              <li>拖拽式题目排序</li>
              <li>自定义分值设置</li>
              <li>一键生成Word试卷</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作流程 -->
    <section class="workflow">
      <div class="container">
        <div class="section-header">
          <h2>简单三步，轻松搞定</h2>
          <p>从文档上传到题库管理，全程自动化处理</p>
        </div>
        <div class="workflow-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>上传文档</h3>
              <p>支持PDF、Word格式，拖拽上传，最大50MB</p>
            </div>
          </div>
          <div class="step-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>智能解析</h3>
              <p>AI自动识别题目结构，提取题干、选项、答案</p>
            </div>
          </div>
          <div class="step-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>管理使用</h3>
              <p>题目自动入库，支持编辑、分类、组卷等操作</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 价格方案 -->
    <section id="pricing" class="pricing">
      <div class="container">
        <div class="section-header">
          <h2>选择适合您的方案</h2>
          <p>从免费体验到专业版本，满足不同需求</p>
        </div>
        <div class="pricing-grid">
          <div class="pricing-card">
            <div class="pricing-header">
              <h3>免费版</h3>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">0</span>
                <span class="period">/月</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><el-icon><Check /></el-icon>PDF转Word：1次/天</li>
                <li><el-icon><Check /></el-icon>试题解析：1次/天</li>
                <li><el-icon><Check /></el-icon>试卷生成：1次/天</li>
                <li><el-icon><Check /></el-icon>基础题库管理</li>
                <li><el-icon><Close /></el-icon>高级公式识别</li>
                <li><el-icon><Close /></el-icon>批量处理</li>
              </ul>
            </div>
            <el-button class="pricing-button" @click="$router.push('/register')">
              免费开始
            </el-button>
          </div>

          <div class="pricing-card featured">
            <div class="pricing-badge">推荐</div>
            <div class="pricing-header">
              <h3>高级版</h3>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">99</span>
                <span class="period">/月</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><el-icon><Check /></el-icon>无限次数使用</li>
                <li><el-icon><Check /></el-icon>高级公式识别</li>
                <li><el-icon><Check /></el-icon>批量文件处理</li>
                <li><el-icon><Check /></el-icon>优先处理队列</li>
                <li><el-icon><Check /></el-icon>高级题库功能</li>
                <li><el-icon><Check /></el-icon>专属客服支持</li>
              </ul>
            </div>
            <el-button type="primary" class="pricing-button" @click="$router.push('/register')">
              立即升级
            </el-button>
          </div>

          <div class="pricing-card">
            <div class="pricing-header">
              <h3>企业版</h3>
              <div class="price">
                <span class="currency">¥</span>
                <span class="amount">299</span>
                <span class="period">/月</span>
              </div>
            </div>
            <div class="pricing-features">
              <ul>
                <li><el-icon><Check /></el-icon>高级版全部功能</li>
                <li><el-icon><Check /></el-icon>多用户管理</li>
                <li><el-icon><Check /></el-icon>数据统计分析</li>
                <li><el-icon><Check /></el-icon>API接口支持</li>
                <li><el-icon><Check /></el-icon>定制化服务</li>
                <li><el-icon><Check /></el-icon>专业技术支持</li>
              </ul>
            </div>
            <el-button class="pricing-button" @click="contactSales">
              联系销售
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 用户评价 -->
    <section class="testimonials">
      <div class="container">
        <div class="section-header">
          <h2>用户怎么说</h2>
          <p>来自全国各地教育工作者的真实反馈</p>
        </div>
        <div class="testimonials-grid">
          <div class="testimonial-card">
            <div class="testimonial-content">
              "d2x大大提高了我的工作效率，以前需要几个小时手动录入的试题，现在几分钟就能完成解析。"
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">
                <el-avatar>张</el-avatar>
              </div>
              <div class="author-info">
                <div class="author-name">张老师</div>
                <div class="author-title">北京某中学数学教师</div>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "公式识别非常准确，特别是物理和数学题目，基本不需要手动修改，节省了大量时间。"
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">
                <el-avatar>李</el-avatar>
              </div>
              <div class="author-info">
                <div class="author-name">李教授</div>
                <div class="author-title">清华大学物理系</div>
              </div>
            </div>
          </div>

          <div class="testimonial-card">
            <div class="testimonial-content">
              "智能组卷功能很实用，可以根据不同班级的情况快速生成个性化试卷，学生反馈很好。"
            </div>
            <div class="testimonial-author">
              <div class="author-avatar">
                <el-avatar>王</el-avatar>
              </div>
              <div class="author-info">
                <div class="author-name">王主任</div>
                <div class="author-title">上海某培训机构教学主任</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta">
      <div class="container">
        <div class="cta-content">
          <h2>准备好开始了吗？</h2>
          <p>加入数万名教育工作者，体验智能化的教学工具</p>
          <div class="cta-actions">
            <el-button type="primary" size="large" @click="$router.push('/register')">
              免费注册
            </el-button>
            <el-button size="large" @click="$router.push('/login')">
              立即登录
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>d2x</h3>
            <p>智能试题解析平台，让教学更高效</p>
            <div class="social-links">
              <el-button circle><el-icon><Message /></el-icon></el-button>
              <el-button circle><el-icon><Phone /></el-icon></el-button>
              <el-button circle><el-icon><ChatDotRound /></el-icon></el-button>
            </div>
          </div>
          <div class="footer-section">
            <h4>产品功能</h4>
            <ul>
              <li><a href="#features">PDF转Word</a></li>
              <li><a href="#features">试题解析</a></li>
              <li><a href="#features">题库管理</a></li>
              <li><a href="#features">智能组卷</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>帮助支持</h4>
            <ul>
              <li><a href="#">使用教程</a></li>
              <li><a href="#">常见问题</a></li>
              <li><a href="#">联系客服</a></li>
              <li><a href="#">意见反馈</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li><a href="#">公司介绍</a></li>
              <li><a href="#">隐私政策</a></li>
              <li><a href="#">服务条款</a></li>
              <li><a href="#">加入我们</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 d2x. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import {
  Star,
  VideoPlay,
  UploadFilled,
  Document,
  EditPen,
  Collection,
  Notebook,
  ArrowRight,
  Check,
  Close,
  Message,
  Phone,
  ChatDotRound,
  Menu
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const mobileMenuVisible = ref(false)

const scrollToDemo = () => {
  const element = document.getElementById('features')
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const contactSales = () => {
  ElMessage.info('请联系客服：************')
}

const toggleMobileMenu = () => {
  mobileMenuVisible.value = !mobileMenuVisible.value
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/')
}
</script>

<style scoped>
.home {
  min-height: 100vh;
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e8eaec;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin: 0;
}

.tagline {
  font-size: 12px;
  color: #666;
  background: #f0f9ff;
  padding: 2px 8px;
  border-radius: 12px;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover {
  color: #409eff;
}

.nav-actions {
  display: flex;
  gap: 12px;
}

.mobile-menu-btn {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f5f7fa;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mobile-menu-btn:hover {
  background: #e8eaec;
}

/* 英雄区域 */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 24px;
}

.highlight {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 32px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 48px;
}

.hero-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 演示卡片 */
.demo-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
}

.demo-header {
  background: #f5f7fa;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e8eaec;
}

.demo-dots {
  display: flex;
  gap: 6px;
}

.demo-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ddd;
}

.demo-dots span:nth-child(1) { background: #ff5f57; }
.demo-dots span:nth-child(2) { background: #ffbd2e; }
.demo-dots span:nth-child(3) { background: #28ca42; }

.demo-title {
  font-size: 14px;
  color: #666;
}

.demo-content {
  padding: 32px;
  color: #333;
}

.demo-upload {
  text-align: center;
  padding: 24px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  margin-bottom: 24px;
}

.demo-upload p {
  margin: 12px 0 0 0;
  color: #666;
}

.demo-progress p {
  margin: 12px 0 0 0;
  color: #666;
  font-size: 14px;
}

/* 通用容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
}

.section-header p {
  font-size: 18px;
  color: #666;
  margin: 0;
}

/* 功能特色 */
.features {
  padding: 80px 0;
  background: #f8fafc;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
}

.feature-card {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.feature-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: white;
}

.pdf-icon { background: linear-gradient(135deg, #409eff, #337ecc); }
.parse-icon { background: linear-gradient(135deg, #67c23a, #529b2e); }
.bank-icon { background: linear-gradient(135deg, #e6a23c, #b88230); }
.exam-icon { background: linear-gradient(135deg, #f56c6c, #c45656); }

.feature-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.feature-list .el-icon {
  color: #67c23a;
}

/* 工作流程 */
.workflow {
  padding: 80px 0;
  background: white;
}

.workflow-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.step {
  text-align: center;
  flex: 1;
}

.step-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff, #337ecc);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin: 0 auto 16px;
}

.step h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.step p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.step-arrow {
  color: #d9d9d9;
  font-size: 24px;
}

/* 价格方案 */
.pricing {
  padding: 80px 0;
  background: #f8fafc;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  transition: transform 0.3s;
}

.pricing-card:hover {
  transform: translateY(-4px);
}

.pricing-card.featured {
  border: 2px solid #409eff;
  transform: scale(1.05);
}

.pricing-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.pricing-header {
  padding: 32px 32px 24px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.pricing-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.currency {
  font-size: 18px;
  color: #666;
}

.amount {
  font-size: 48px;
  font-weight: 700;
  color: #333;
}

.period {
  font-size: 16px;
  color: #666;
}

.pricing-features {
  padding: 24px 32px;
}

.pricing-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
}

.pricing-features .el-icon {
  font-size: 16px;
}

.pricing-button {
  width: calc(100% - 64px);
  margin: 0 32px 32px;
  height: 44px;
}

/* 用户评价 */
.testimonials {
  padding: 80px 0;
  background: white;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.testimonial-card {
  background: #f8fafc;
  padding: 32px;
  border-radius: 12px;
  border-left: 4px solid #409eff;
}

.testimonial-content {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 24px;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 16px;
}

.author-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.author-title {
  font-size: 14px;
  color: #666;
}

/* CTA区域 */
.cta {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.cta h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
}

.cta p {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.cta-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 页脚 */
.footer {
  background: #1a1a1a;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
}

.footer-section h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #ccc;
}

.footer-section p {
  color: #999;
  line-height: 1.6;
  margin-bottom: 16px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #999;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: white;
}

.social-links {
  display: flex;
  gap: 12px;
}

.footer-bottom {
  border-top: 1px solid #333;
  padding-top: 20px;
  text-align: center;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .demo-card {
    transform: none;
  }

  .workflow-steps {
    flex-direction: column;
    gap: 24px;
  }

  .step-arrow {
    transform: rotate(90deg);
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .pricing-card.featured {
    transform: none;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 32px;
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: 0 16px;
  }

  .hero {
    padding: 100px 0 60px;
  }

  .hero-title {
    font-size: 28px;
  }

  .section-header h2 {
    font-size: 28px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-card {
    padding: 24px;
  }
}
</style>