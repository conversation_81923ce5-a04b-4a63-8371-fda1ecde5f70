<template>
  <Layout>
    <div class="tasks">
      <div class="page-header">
        <h2>任务中心</h2>
        <p>查看文件处理和试卷生成任务的进度</p>
      </div>

      <!-- 任务统计 -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-icon pending">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.pending }}</div>
            <div class="stat-label">等待中</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon processing">
            <el-icon><Loading /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.processing }}</div>
            <div class="stat-label">处理中</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon completed">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon failed">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ taskStats.failed }}</div>
            <div class="stat-label">失败</div>
          </div>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="tasks-section">
        <div class="card">
          <div class="card-header">
            <h3>任务列表</h3>
            <div class="header-actions">
              <el-select v-model="statusFilter" placeholder="状态筛选" @change="loadTasks">
                <el-option label="全部" value="" />
                <el-option label="等待中" value="pending" />
                <el-option label="处理中" value="processing" />
                <el-option label="已完成" value="completed" />
                <el-option label="失败" value="failed" />
              </el-select>
              <el-button @click="loadTasks" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>
          
          <div class="tasks-list" v-loading="loading">
            <div
              v-for="task in taskList"
              :key="task.id"
              class="task-item"
            >
              <div class="task-icon">
                <el-icon :color="getTaskIconColor(task.taskType)">
                  <component :is="getTaskIcon(task.taskType)" />
                </el-icon>
              </div>
              
              <div class="task-content">
                <div class="task-header">
                  <h4 class="task-title">{{ getTaskTitle(task.taskType) }}</h4>
                  <el-tag :type="getStatusType(task.status)" size="small">
                    {{ getStatusText(task.status) }}
                  </el-tag>
                </div>
                
                <div class="task-info">
                  <span v-if="task.fileUpload && task.fileUpload.originalFilename">
                    文件：{{ formatFilename(task.fileUpload.originalFilename) }}
                  </span>
                  <span v-else-if="task.fileUploadId">
                    文件ID：{{ task.fileUploadId }}
                  </span>
                  <span>创建时间：{{ formatTime(task.createdAt) }}</span>
                  <span v-if="task.completedAt">
                    完成时间：{{ formatTime(task.completedAt) }}
                  </span>
                </div>
                
                <!-- 进度条 -->
                <div v-if="task.status === 'processing'" class="task-progress">
                  <el-progress :percentage="task.progress" :status="task.progress === 100 ? 'success' : undefined" />
                </div>
                
                <!-- 错误信息 -->
                <div v-if="task.status === 'failed' && task.errorMessage" class="task-error">
                  <el-alert :title="task.errorMessage" type="error" :closable="false" />
                </div>
                
                <!-- 结果信息 -->
                <div v-if="task.status === 'completed' && task.resultData" class="task-result">
                  <div v-if="task.taskType === 'question_parsing'" class="result-info">
                    <el-icon><Collection /></el-icon>
                    <span>解析出 {{ task.resultData.questionsCount }} 道题目</span>
                  </div>
                  <div v-if="task.taskType === 'pdf_to_word'" class="result-info">
                    <el-icon><Document /></el-icon>
                    <span>转换完成</span>
                  </div>
                  <div v-if="task.taskType === 'exam_generation'" class="result-info">
                    <el-icon><Notebook /></el-icon>
                    <span>试卷生成完成</span>
                  </div>
                </div>
              </div>
              
              <div class="task-actions">
                <el-button
                  v-if="task.status === 'completed' && task.resultData?.outputPath"
                  type="primary"
                  size="small"
                  @click="downloadResult(task.id)"
                >
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                
                <el-button
                  v-if="task.status === 'failed'"
                  type="warning"
                  size="small"
                  @click="retryTask(task.id)"
                >
                  <el-icon><RefreshRight /></el-icon>
                  重试
                </el-button>
                
                <el-button
                  v-if="task.status === 'processing' || task.status === 'pending'"
                  type="danger"
                  size="small"
                  @click="cancelTask(task.id)"
                >
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
              </div>
            </div>
            
            <el-empty v-if="!loading && taskList.length === 0" description="暂无任务数据" />
          </div>
          
          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="pagination.total > 0">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.limit"
              :total="pagination.total"
              :page-sizes="[10, 20, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadTasks"
              @current-change="loadTasks"
            />
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import {
  Clock,
  Loading,
  CircleCheck,
  CircleClose,
  Refresh,
  Document,
  EditPen,
  Notebook,
  Collection,
  Download,
  RefreshRight,
  Close
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { http, downloadFile } from '@/utils/http'

const loading = ref(false)
const statusFilter = ref('')
const taskList = ref([])
const refreshTimer = ref<NodeJS.Timeout>()

const taskStats = reactive({
  pending: 0,
  processing: 0,
  completed: 0,
  failed: 0
})

const pagination = ref({
  page: 1,
  limit: 20,
  total: 0
})

const loadTasks = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit,
      status: statusFilter.value || undefined
    }
    
    const response = await http.get('/task', { params })
    taskList.value = response.tasks || []
    pagination.value.total = response.pagination?.total || 0
    
    // 更新统计信息
    updateTaskStats()
  } catch (error) {
    console.error('Load tasks error:', error)
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const updateTaskStats = () => {
  taskStats.pending = taskList.value.filter(t => t.status === 'pending').length
  taskStats.processing = taskList.value.filter(t => t.status === 'processing').length
  taskStats.completed = taskList.value.filter(t => t.status === 'completed').length
  taskStats.failed = taskList.value.filter(t => t.status === 'failed').length
}

const downloadResult = async (taskId: string) => {
  try {
    await downloadFile(`/task/${taskId}/download`)
  } catch (error) {
    console.error('Download result error:', error)
    ElMessage.error('下载失败')
  }
}

const retryTask = async (taskId: string) => {
  try {
    await http.post(`/task/${taskId}/retry`)
    ElMessage.success('任务已重新提交')
    loadTasks()
  } catch (error) {
    console.error('Retry task error:', error)
    ElMessage.error('重试任务失败')
  }
}

const cancelTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要取消这个任务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await http.post(`/task/${taskId}/cancel`)
    ElMessage.success('任务已取消')
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Cancel task error:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

const getTaskIcon = (taskType: string) => {
  switch (taskType) {
    case 'pdf_to_word': return Document
    case 'question_parsing': return EditPen
    case 'exam_generation': return Notebook
    default: return Document
  }
}

const getTaskIconColor = (taskType: string) => {
  switch (taskType) {
    case 'pdf_to_word': return '#409eff'
    case 'question_parsing': return '#67c23a'
    case 'exam_generation': return '#e6a23c'
    default: return '#909399'
  }
}

const getTaskTitle = (taskType: string) => {
  switch (taskType) {
    case 'pdf_to_word': return 'PDF转Word'
    case 'question_parsing': return '试题解析'
    case 'exam_generation': return '试卷生成'
    default: return '未知任务'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'processing': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'processing': return '处理中'
    case 'failed': return '失败'
    case 'pending': return '等待中'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化文件名，处理编码问题
const formatFilename = (filename: string) => {
  if (!filename) return '未知文件'

  try {
    // 尝试解码可能被错误编码的文件名
    if (filename.includes('%')) {
      return decodeURIComponent(filename)
    }

    // 检查是否是乱码（包含特殊字符）
    if (/[^\x00-\x7F]/.test(filename) && !/[\u4e00-\u9fa5]/.test(filename)) {
      // 可能是编码问题，尝试重新编码
      try {
        return decodeURIComponent(escape(filename))
      } catch {
        return filename
      }
    }

    return filename
  } catch (error) {
    console.warn('文件名格式化失败:', error)
    return filename || '未知文件'
  }
}

// 自动刷新处理中的任务
const startAutoRefresh = () => {
  refreshTimer.value = setInterval(() => {
    const hasProcessingTasks = taskList.value.some(t => 
      t.status === 'processing' || t.status === 'pending'
    )
    
    if (hasProcessingTasks) {
      loadTasks()
    }
  }, 5000) // 每5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = undefined
  }
}

onMounted(() => {
  loadTasks()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.tasks {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.pending { background: #909399; }
.stat-icon.processing { background: #e6a23c; }
.stat-icon.completed { background: #67c23a; }
.stat-icon.failed { background: #f56c6c; }

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.tasks-section .card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8eaec;
}

.card-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.tasks-list {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 400px;
}

.task-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e8eaec;
  border-radius: 8px;
  background: #fafafa;
}

.task-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.task-content {
  flex: 1;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.task-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.task-progress {
  margin: 12px 0;
}

.task-error {
  margin: 12px 0;
}

.task-result {
  margin: 12px 0;
}

.result-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #67c23a;
}

.task-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.pagination-wrapper {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e8eaec;
}

@media (max-width: 768px) {
  .stats-section {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .task-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .task-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }
  
  .task-info {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
