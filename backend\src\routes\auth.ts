import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const authController = new AuthController();

// 用户注册
router.post('/register', authController.register.bind(authController));

// 用户登录
router.post('/login', authController.login.bind(authController));

// 刷新令牌
router.post('/refresh', authController.refreshToken.bind(authController));

// 获取当前用户信息
router.get('/me', authenticateToken, authController.getCurrentUser.bind(authController));

// 用户登出
router.post('/logout', authenticateToken, authController.logout.bind(authController));

// 修改密码
router.post('/change-password', authenticateToken, authController.changePassword.bind(authController));

// 忘记密码
router.post('/forgot-password', authController.forgotPassword.bind(authController));

// 重置密码
router.post('/reset-password', authController.resetPassword.bind(authController));

// 邮箱验证
router.get('/verify-email', authController.verifyEmail.bind(authController));

// 重新发送验证邮件
router.post('/resend-verification', authController.resendVerificationEmail.bind(authController));

export default router;
