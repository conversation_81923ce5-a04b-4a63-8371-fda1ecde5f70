<template>
  <Layout>
    <div class="profile">
      <div class="page-header">
        <h2>个人资料</h2>
        <p>管理您的账户信息和偏好设置</p>
      </div>

      <div class="profile-container">
        <!-- 基本信息 -->
        <div class="card">
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
          <div class="profile-form">
            <div class="avatar-section">
              <el-avatar :src="form.avatarUrl" :size="80">
                {{ form.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <el-button size="small" @click="uploadAvatar">更换头像</el-button>
            </div>
            
            <el-form :model="form" label-width="100px" @submit.prevent="updateProfile">
              <el-form-item label="用户名">
                <el-input v-model="form.username" disabled />
              </el-form-item>
              
              <el-form-item label="邮箱">
                <el-input v-model="form.email" disabled />
              </el-form-item>
              
              <el-form-item label="真实姓名">
                <el-input v-model="form.fullName" placeholder="请输入真实姓名" />
              </el-form-item>
              
              <el-form-item label="用户类型">
                <el-tag :type="getUserTypeColor(form.userType)" size="large">
                  {{ getUserTypeText(form.userType) }}
                </el-tag>
                <el-button v-if="form.userType === 'free'" type="primary" size="small" style="margin-left: 12px;">
                  升级到高级会员
                </el-button>
              </el-form-item>
              
              <el-form-item label="注册时间">
                <span>{{ formatTime(form.createdAt) }}</span>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="updateProfile" :loading="updating">
                  保存修改
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 使用统计 -->
        <div class="card">
          <div class="card-header">
            <h3>使用统计</h3>
          </div>
          <div class="usage-stats">
            <div class="usage-item">
              <div class="usage-icon">
                <el-icon size="24" color="#409eff"><Document /></el-icon>
              </div>
              <div class="usage-content">
                <div class="usage-title">PDF转换</div>
                <div class="usage-count">今日: {{ dailyUsage.pdfConvert }} 次</div>
              </div>
            </div>
            
            <div class="usage-item">
              <div class="usage-icon">
                <el-icon size="24" color="#67c23a"><EditPen /></el-icon>
              </div>
              <div class="usage-content">
                <div class="usage-title">试题解析</div>
                <div class="usage-count">今日: {{ dailyUsage.questionParse }} 次</div>
              </div>
            </div>
            
            <div class="usage-item">
              <div class="usage-icon">
                <el-icon size="24" color="#e6a23c"><Notebook /></el-icon>
              </div>
              <div class="usage-content">
                <div class="usage-title">试卷生成</div>
                <div class="usage-count">今日: {{ dailyUsage.examGenerate }} 次</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全设置 -->
        <div class="card">
          <div class="card-header">
            <h3>安全设置</h3>
          </div>
          <div class="security-section">
            <div class="security-item">
              <div class="security-info">
                <h4>登录密码</h4>
                <p>定期更换密码可以提高账户安全性</p>
              </div>
              <el-button @click="showChangePasswordDialog">修改密码</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 修改密码对话框 -->
      <el-dialog v-model="passwordDialogVisible" title="修改密码" width="400px">
        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input
              v-model="passwordForm.currentPassword"
              type="password"
              placeholder="请输入当前密码"
              show-password
            />
          </el-form-item>
          
          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>
          
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请确认新密码"
              show-password
            />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changePassword" :loading="changingPassword">
            确定
          </el-button>
        </template>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'
import {
  Document,
  EditPen,
  Notebook
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import { http } from '@/utils/http'

const authStore = useAuthStore()

const updating = ref(false)
const changingPassword = ref(false)
const passwordDialogVisible = ref(false)
const passwordFormRef = ref<FormInstance>()

const form = reactive({
  username: '',
  email: '',
  fullName: '',
  avatarUrl: '',
  userType: 'free',
  createdAt: ''
})

const dailyUsage = reactive({
  pdfConvert: 0,
  questionParse: 0,
  examGenerate: 0
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const loadProfile = async () => {
  try {
    const response = await http.get('/user/profile')
    const user = response.user
    
    Object.assign(form, {
      username: user.username,
      email: user.email,
      fullName: user.fullName || '',
      avatarUrl: user.avatarUrl || '',
      userType: user.userType,
      createdAt: user.createdAt
    })
    
    Object.assign(dailyUsage, user.dailyUsage || {
      pdfConvert: 0,
      questionParse: 0,
      examGenerate: 0
    })
  } catch (error) {
    console.error('Load profile error:', error)
    ElMessage.error('加载用户资料失败')
  }
}

const updateProfile = async () => {
  updating.value = true
  try {
    await http.put('/user/profile', {
      fullName: form.fullName,
      avatarUrl: form.avatarUrl
    })
    
    ElMessage.success('资料更新成功')
    
    // 更新store中的用户信息
    await authStore.getCurrentUser()
  } catch (error) {
    console.error('Update profile error:', error)
    ElMessage.error('更新资料失败')
  } finally {
    updating.value = false
  }
}

const uploadAvatar = () => {
  ElMessage.info('头像上传功能开发中...')
}

const showChangePasswordDialog = () => {
  passwordDialogVisible.value = true
  
  // 重置表单
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    
    changingPassword.value = true
    
    await authStore.changePassword(passwordForm.currentPassword, passwordForm.newPassword)
    
    passwordDialogVisible.value = false
    
    // 重置表单
    Object.assign(passwordForm, {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    })
  } catch (error) {
    console.error('Change password error:', error)
  } finally {
    changingPassword.value = false
  }
}

const getUserTypeColor = (userType: string) => {
  switch (userType) {
    case 'admin': return 'danger'
    case 'premium': return 'warning'
    default: return 'info'
  }
}

const getUserTypeText = (userType: string) => {
  switch (userType) {
    case 'admin': return '管理员'
    case 'premium': return '高级会员'
    default: return '免费用户'
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

onMounted(() => {
  loadProfile()
})
</script>

<style scoped>
.profile {
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.page-header p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.profile-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #e8eaec;
}

.card-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.profile-form {
  padding: 20px;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.usage-stats {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.usage-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.usage-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.usage-count {
  font-size: 12px;
  color: #666;
}

.security-section {
  padding: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
}

.security-info h4 {
  font-size: 16px;
  margin: 0 0 4px 0;
  color: #333;
}

.security-info p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

@media (max-width: 768px) {
  .avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .usage-stats {
    grid-template-columns: 1fr;
  }
  
  .security-item {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
