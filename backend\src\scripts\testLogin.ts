#!/usr/bin/env ts-node

/**
 * 登录功能测试脚本
 * 测试用户名和邮箱登录
 */

import dotenv from 'dotenv';
import axios from 'axios';

// 加载环境变量
dotenv.config();

const API_BASE_URL = 'http://localhost:3000/api';

async function testLogin() {
  console.log('🧪 测试登录功能...');
  console.log('================================');

  // 测试数据
  const testCases = [
    {
      name: '邮箱登录 - 管理员',
      credentials: {
        email: '<EMAIL>',
        password: 'admin123456'
      }
    },
    {
      name: '用户名登录 - 管理员',
      credentials: {
        email: 'admin',
        password: 'admin123456'
      }
    },
    {
      name: '邮箱登录 - 测试用户',
      credentials: {
        email: '<EMAIL>',
        password: 'test123456'
      }
    },
    {
      name: '用户名登录 - 测试用户',
      credentials: {
        email: 'testuser',
        password: 'test123456'
      }
    },
    {
      name: '错误的邮箱',
      credentials: {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }
    },
    {
      name: '错误的用户名',
      credentials: {
        email: 'wronguser',
        password: 'wrongpassword'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🔍 测试: ${testCase.name}`);
    console.log(`   输入: ${testCase.credentials.email}`);
    
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, testCase.credentials);
      
      if (response.status === 200) {
        console.log('✅ 登录成功');
        console.log(`   用户: ${response.data.user.username} (${response.data.user.email})`);
        console.log(`   类型: ${response.data.user.userType}`);
        console.log(`   Token: ${response.data.token.substring(0, 20)}...`);
      }
    } catch (error: any) {
      if (error.response) {
        console.log(`❌ 登录失败: ${error.response.data.error}`);
        console.log(`   状态码: ${error.response.status}`);
      } else {
        console.log(`❌ 网络错误: ${error.message}`);
      }
    }
  }

  console.log('\n🎯 测试总结:');
  console.log('- 邮箱登录应该成功');
  console.log('- 用户名登录应该成功');
  console.log('- 错误的凭据应该失败');
  console.log('- 错误信息应该统一显示"用户名/邮箱或密码错误"');
}

// 运行测试
testLogin().catch(console.error);
