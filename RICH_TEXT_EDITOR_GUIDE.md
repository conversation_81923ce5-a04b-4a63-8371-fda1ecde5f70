# 📝 富文本编辑器功能指南

## 🎯 功能概述

d2x平台集成了功能强大的富文本编辑器，专为题目创建和编辑设计，支持多种格式文本、数学公式、图片上传、表格插入等高级功能。

## ✨ 核心特性

### 1. 富文本编辑
- **文本格式**: 粗体、斜体、下划线、删除线
- **段落格式**: 标题、引用、代码块
- **列表**: 有序列表、无序列表
- **对齐方式**: 左对齐、居中、右对齐
- **字体设置**: 字体大小、颜色、背景色
- **特殊格式**: 上标、下标、缩进

### 2. 数学公式支持
- **LaTeX语法**: 支持完整的LaTeX数学公式
- **实时预览**: 输入时即时预览公式效果
- **公式示例**: 内置常用公式模板
- **渲染引擎**: 使用KaTeX高质量渲染

### 3. 图片管理
- **多格式支持**: JPG、PNG、GIF、WebP
- **拖拽上传**: 支持拖拽文件上传
- **大小限制**: 单个图片最大5MB
- **自动压缩**: 智能优化图片大小
- **预览功能**: 上传前预览图片效果

### 4. 表格功能
- **灵活配置**: 自定义行数和列数
- **表头设置**: 可选择是否包含表头
- **样式美化**: 自动应用表格样式
- **编辑友好**: 支持表格内容编辑

## 🔧 使用指南

### 基础编辑操作

#### 文本格式化
1. **选择文本**: 鼠标选中要格式化的文本
2. **应用格式**: 点击工具栏相应按钮
3. **快捷键**: 支持Ctrl+B（粗体）、Ctrl+I（斜体）等

#### 段落设置
1. **标题**: 选择标题级别（H1-H6）
2. **列表**: 点击有序/无序列表按钮
3. **引用**: 使用引用格式突出重要内容

### 数学公式编辑

#### 插入公式
1. 点击工具栏"公式"按钮
2. 在弹出对话框中输入LaTeX代码
3. 实时预览公式效果
4. 点击"插入公式"完成

#### 常用公式示例
```latex
分数: \frac{a}{b}
根号: \sqrt{x}
上标: x^2
下标: x_1
求和: \sum_{i=1}^{n} x_i
积分: \int_{a}^{b} f(x)dx
极限: \lim_{x \to \infty} f(x)
矩阵: \begin{pmatrix} a & b \\ c & d \end{pmatrix}
```

#### 公式编辑技巧
- **预览功能**: 输入时实时查看效果
- **语法检查**: 自动检测语法错误
- **示例模板**: 点击示例快速插入
- **编辑修改**: 双击公式可重新编辑

### 图片上传管理

#### 上传方式
1. **点击上传**: 点击"图片"按钮选择文件
2. **拖拽上传**: 直接拖拽图片到上传区域
3. **预览确认**: 上传前预览图片效果
4. **插入编辑器**: 确认后插入到编辑器

#### 图片要求
- **格式**: JPG、PNG、GIF、WebP
- **大小**: 最大5MB
- **尺寸**: 建议宽度不超过800px
- **质量**: 清晰度适中，避免过大文件

### 表格插入使用

#### 创建表格
1. 点击工具栏"表格"按钮
2. 设置行数和列数（最大20行×10列）
3. 选择是否包含表头
4. 点击"插入表格"完成

#### 表格编辑
- **内容编辑**: 直接在单元格中输入内容
- **格式设置**: 支持单元格内文本格式化
- **表格样式**: 自动应用美观的边框和样式

## 🎨 界面说明

### 编辑器布局
```
┌─────────────────────────────────────────┐
│ [公式] [图片] [表格]  自定义工具栏      │
├─────────────────────────────────────────┤
│ [B] [I] [U] [颜色] [对齐] 标准工具栏    │
├─────────────────────────────────────────┤
│                                         │
│           编辑区域                      │
│                                         │
│                                         │
└─────────────────────────────────────────┘
```

### 工具栏功能
- **自定义工具栏**: 公式、图片、表格等特殊功能
- **标准工具栏**: 文本格式、段落、列表等基础功能
- **编辑区域**: 主要内容编辑区域，支持所见即所得

## 📋 题目编辑应用

### 题目创建流程
1. **基本信息**: 填写题号、类型、学科、难度
2. **题目描述**: 使用富文本编辑器输入题干
3. **选项设置**: 为选择题添加选项（支持富文本）
4. **正确答案**: 设置标准答案（支持富文本）
5. **解析说明**: 添加详细解析（支持富文本）
6. **标签管理**: 添加分类标签

### 支持的题目类型
- **单选题**: 单个正确答案
- **多选题**: 多个正确答案
- **判断题**: 正确/错误选择
- **填空题**: 文本输入答案
- **问答题**: 开放性回答
- **计算题**: 数学计算过程

### 富文本应用场景
- **复杂题干**: 包含公式、图表的题目描述
- **图文并茂**: 题目配图和说明图片
- **数学公式**: 各种数学表达式和符号
- **表格数据**: 数据分析和统计题目
- **格式化答案**: 结构化的参考答案

## 🔧 技术特性

### 编辑器引擎
- **Quill.js**: 现代化富文本编辑器
- **模块化设计**: 可扩展的功能模块
- **跨浏览器**: 兼容主流浏览器
- **移动友好**: 支持触屏设备

### 公式渲染
- **KaTeX**: 快速数学公式渲染
- **LaTeX兼容**: 支持标准LaTeX语法
- **高质量输出**: 矢量图形渲染
- **性能优化**: 快速渲染大量公式

### 图片处理
- **安全上传**: 文件类型和大小验证
- **云存储**: 可靠的图片存储服务
- **CDN加速**: 快速图片加载
- **格式转换**: 自动优化图片格式

## 📊 使用统计

### 支持格式
- **文本格式**: 15+ 种文本样式
- **公式类型**: 100+ 种数学符号
- **图片格式**: 4种主流格式
- **表格规格**: 最大20×10单元格

### 性能指标
- **加载速度**: < 2秒完整加载
- **响应时间**: < 100ms操作响应
- **文件大小**: 编辑器核心 < 500KB
- **兼容性**: 支持IE11+所有现代浏览器

## 🎯 最佳实践

### 内容创建建议
1. **结构清晰**: 使用标题和段落组织内容
2. **公式规范**: 遵循标准LaTeX语法
3. **图片优化**: 控制图片大小和质量
4. **表格简洁**: 避免过于复杂的表格结构

### 性能优化
1. **图片压缩**: 上传前适当压缩图片
2. **公式缓存**: 重复公式会自动缓存
3. **内容分段**: 长内容分段编辑
4. **定期保存**: 及时保存编辑内容

### 兼容性考虑
1. **浏览器测试**: 在不同浏览器中测试效果
2. **移动适配**: 确保移动设备正常显示
3. **导出格式**: 考虑内容的导出兼容性

## 🎉 总结

富文本编辑器为d2x平台提供了强大的内容创建能力：

- ✅ **功能完整**: 支持文本、公式、图片、表格等多种元素
- ✅ **操作简单**: 直观的界面和便捷的操作方式
- ✅ **质量保证**: 高质量的渲染效果和稳定性能
- ✅ **扩展性强**: 模块化设计支持功能扩展

通过富文本编辑器，用户可以创建专业、美观、功能丰富的题目内容，大大提升了题库管理的效率和质量！
