/**
 * 缓存工具类
 * 支持Redis缓存（可选）和内存缓存（备用）
 */

import { getRedisClient, isRedisEnabled } from '../config/redis';

// 内存缓存（当Redis未启用时使用）
const memoryCache = new Map<string, { value: any; expiry: number }>();

export class CacheService {
  
  /**
   * 设置缓存
   */
  static async set(key: string, value: any, ttlSeconds: number = 3600): Promise<void> {
    if (isRedisEnabled()) {
      const redis = getRedisClient();
      if (redis) {
        await redis.setEx(key, ttlSeconds, JSON.stringify(value));
        return;
      }
    }
    
    // 使用内存缓存作为备用
    const expiry = Date.now() + (ttlSeconds * 1000);
    memoryCache.set(key, { value, expiry });
  }

  /**
   * 获取缓存
   */
  static async get<T>(key: string): Promise<T | null> {
    if (isRedisEnabled()) {
      const redis = getRedisClient();
      if (redis) {
        const result = await redis.get(key);
        return result ? JSON.parse(result) : null;
      }
    }
    
    // 使用内存缓存作为备用
    const cached = memoryCache.get(key);
    if (cached) {
      if (Date.now() > cached.expiry) {
        memoryCache.delete(key);
        return null;
      }
      return cached.value;
    }
    
    return null;
  }

  /**
   * 删除缓存
   */
  static async del(key: string): Promise<void> {
    if (isRedisEnabled()) {
      const redis = getRedisClient();
      if (redis) {
        await redis.del(key);
        return;
      }
    }
    
    // 从内存缓存中删除
    memoryCache.delete(key);
  }

  /**
   * 检查缓存是否存在
   */
  static async exists(key: string): Promise<boolean> {
    if (isRedisEnabled()) {
      const redis = getRedisClient();
      if (redis) {
        return (await redis.exists(key)) === 1;
      }
    }
    
    // 检查内存缓存
    const cached = memoryCache.get(key);
    if (cached) {
      if (Date.now() > cached.expiry) {
        memoryCache.delete(key);
        return false;
      }
      return true;
    }
    
    return false;
  }

  /**
   * 清空所有缓存
   */
  static async clear(): Promise<void> {
    if (isRedisEnabled()) {
      const redis = getRedisClient();
      if (redis) {
        await redis.flushAll();
        return;
      }
    }
    
    // 清空内存缓存
    memoryCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  static getCacheInfo(): { type: string; size: number } {
    if (isRedisEnabled()) {
      return { type: 'Redis', size: -1 }; // Redis大小需要单独查询
    }
    
    return { type: 'Memory', size: memoryCache.size };
  }
}

// 定期清理过期的内存缓存
setInterval(() => {
  if (!isRedisEnabled()) {
    const now = Date.now();
    for (const [key, cached] of memoryCache.entries()) {
      if (now > cached.expiry) {
        memoryCache.delete(key);
      }
    }
  }
}, 60000); // 每分钟清理一次
