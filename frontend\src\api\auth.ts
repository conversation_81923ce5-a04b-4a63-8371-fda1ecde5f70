import { http } from '@/utils/http'
import type {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest
} from '@/types/auth'

export const authApi = {
  // 用户登录
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return http.post('/auth/login', data)
  },

  // 用户注册
  register: (data: RegisterRequest): Promise<RegisterResponse> => {
    return http.post('/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return http.get('/auth/me')
  },

  // 刷新令牌
  refreshToken: (token: string): Promise<RefreshTokenResponse> => {
    return http.post('/auth/refresh', { token })
  },

  // 用户登出
  logout: () => {
    return http.post('/auth/logout')
  },

  // 修改密码
  changePassword: (data: ChangePasswordRequest) => {
    return http.post('/auth/change-password', data)
  },

  // 忘记密码
  forgotPassword: (data: ForgotPasswordRequest) => {
    return http.post('/auth/forgot-password', data)
  },

  // 重置密码
  resetPassword: (data: ResetPasswordRequest) => {
    return http.post('/auth/reset-password', data)
  },

  // 验证邮箱
  verifyEmail: (token: string, email: string) => {
    return http.get('/auth/verify-email', { params: { token, email } })
  },

  // 重新发送验证邮件
  resendVerificationEmail: (email: string) => {
    return http.post('/auth/resend-verification', { email })
  }
}
