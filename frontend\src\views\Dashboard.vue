<template>
  <Layout>
    <div class="dashboard">
      <!-- 欢迎信息 -->
      <div class="welcome-section">
        <div class="welcome-content">
          <h2>欢迎回来，{{ authStore.user?.fullName || authStore.user?.username }}！</h2>
          <p>今天是 {{ currentDate }}，开始您的智能教学之旅吧</p>
        </div>
        <div class="user-type-badge">
          <el-tag :type="userTypeColor" size="large">
            {{ userTypeText }}
          </el-tag>
        </div>
      </div>

      <!-- 功能卡片 -->
      <div class="feature-cards">
        <div class="feature-card" @click="navigateTo('/pdf-convert')">
          <div class="card-icon pdf-icon">
            <el-icon size="32"><Document /></el-icon>
          </div>
          <div class="card-content">
            <h3>PDF转Word</h3>
            <p>快速将PDF文档转换为可编辑的Word格式</p>
            <div class="usage-info">
              今日使用: {{ getUsageText('pdfConvert') }}
            </div>
          </div>
          <div class="card-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="feature-card" @click="navigateTo('/question-parse')">
          <div class="card-icon parse-icon">
            <el-icon size="32"><EditPen /></el-icon>
          </div>
          <div class="card-content">
            <h3>试题解析</h3>
            <p>智能解析试卷文档，自动提取题目信息</p>
            <div class="usage-info">
              今日使用: {{ getUsageText('questionParse') }}
            </div>
          </div>
          <div class="card-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>

        <div class="feature-card" @click="navigateTo('/exam-builder')">
          <div class="card-icon exam-icon">
            <el-icon size="32"><Notebook /></el-icon>
          </div>
          <div class="card-content">
            <h3>智能组卷</h3>
            <p>从题库中选择题目，快速生成试卷</p>
            <div class="usage-info">
              今日使用: {{ getUsageText('examGenerate') }}
            </div>
          </div>
          <div class="card-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon size="24" color="#409eff"><Collection /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.questionsCount }}</div>
              <div class="stat-label">题库题目</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon size="24" color="#67c23a"><Notebook /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.examsCount }}</div>
              <div class="stat-label">创建试卷</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon size="24" color="#e6a23c"><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.filesCount }}</div>
              <div class="stat-label">处理文件</div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <el-icon size="24" color="#f56c6c"><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.tasksCount }}</div>
              <div class="stat-label">处理任务</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="recent-section">
        <div class="section-header">
          <h3>最近活动</h3>
          <el-link type="primary" @click="navigateTo('/tasks')">查看全部</el-link>
        </div>
        
        <div class="activity-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">
              <el-icon :color="getActivityIconColor(activity.type)">
                <component :is="getActivityIcon(activity.type)" />
              </el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ getActivityTitle(activity) }}</div>
              <div class="activity-time">{{ formatTime(activity.createdAt) }}</div>
            </div>
            <div class="activity-status">
              <el-tag :type="getStatusType(activity.status)" size="small">
                {{ getStatusText(activity.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import {
  Document,
  EditPen,
  Notebook,
  ArrowRight,
  Collection,
  Clock
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import { http } from '@/utils/http'

const router = useRouter()
const authStore = useAuthStore()

const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日')
})

const userTypeColor = computed(() => {
  switch (authStore.user?.userType) {
    case 'admin': return 'danger'
    case 'premium': return 'warning'
    default: return 'info'
  }
})

const userTypeText = computed(() => {
  switch (authStore.user?.userType) {
    case 'admin': return '管理员'
    case 'premium': return '高级会员'
    default: return '免费用户'
  }
})

const stats = ref({
  questionsCount: 0,
  examsCount: 0,
  filesCount: 0,
  tasksCount: 0
})

const recentActivities = ref([])

const getUsageText = (feature: 'pdfConvert' | 'questionParse' | 'examGenerate') => {
  const usage = authStore.getFeatureUsage(feature)
  if (usage.limit === -1) {
    return `${usage.used} 次`
  }
  return `${usage.used}/${usage.limit}`
}

const navigateTo = (path: string) => {
  router.push(path)
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'pdf_to_word': return Document
    case 'question_parsing': return EditPen
    case 'exam_generation': return Notebook
    default: return Document
  }
}

const getActivityIconColor = (type: string) => {
  switch (type) {
    case 'pdf_to_word': return '#409eff'
    case 'question_parsing': return '#67c23a'
    case 'exam_generation': return '#e6a23c'
    default: return '#909399'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed': return 'success'
    case 'processing': return 'warning'
    case 'failed': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'processing': return '处理中'
    case 'failed': return '失败'
    case 'pending': return '等待中'
    default: return '未知'
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 生成活动标题
const getActivityTitle = (activity: any) => {
  // 如果有title字段，直接使用
  if (activity.title) {
    return activity.title
  }

  // 根据任务类型和文件信息生成标题
  const taskTypeMap = {
    'pdf_convert': 'PDF转Word',
    'question_parse': '题目解析',
    'exam_generate': '试卷生成'
  }

  const taskTypeName = taskTypeMap[activity.taskType] || '文件处理'

  // 如果有关联的文件信息
  if (activity.fileUpload && activity.fileUpload.originalFilename) {
    const filename = formatFilename(activity.fileUpload.originalFilename)
    return `${taskTypeName} - ${filename}`
  }

  return taskTypeName
}

// 格式化文件名，处理编码问题
const formatFilename = (filename: string) => {
  if (!filename) return '未知文件'

  try {
    // 尝试解码可能被错误编码的文件名
    if (filename.includes('%')) {
      return decodeURIComponent(filename)
    }

    // 检查是否是乱码（包含特殊字符）
    if (/[^\x00-\x7F]/.test(filename) && !/[\u4e00-\u9fa5]/.test(filename)) {
      // 可能是编码问题，尝试重新编码
      try {
        return decodeURIComponent(escape(filename))
      } catch {
        return filename
      }
    }

    return filename
  } catch (error) {
    console.warn('文件名格式化失败:', error)
    return filename || '未知文件'
  }
}

const loadStats = async () => {
  try {
    const response = await http.get('/user/stats')
    stats.value = response.stats
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

const loadRecentActivities = async () => {
  try {
    const response = await http.get('/task?page=1&limit=5')
    recentActivities.value = response.tasks || []
  } catch (error) {
    console.error('Load recent activities error:', error)
  }
}

onMounted(() => {
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h2 {
  font-size: 24px;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.welcome-content p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pdf-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  color: white;
}

.parse-icon {
  background: linear-gradient(135deg, #67c23a, #529b2e);
  color: white;
}

.exam-icon {
  background: linear-gradient(135deg, #e6a23c, #b88230);
  color: white;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: #333;
}

.card-content p {
  font-size: 14px;
  color: #666;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.usage-info {
  font-size: 12px;
  color: #999;
}

.card-arrow {
  color: #c0c4cc;
}

.stats-section {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.recent-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.activity-item:hover {
  background: #f5f7fa;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .feature-cards {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .feature-card {
    padding: 16px;
  }
  
  .card-icon {
    width: 48px;
    height: 48px;
  }
}
</style>
