import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';

// 上传类型
export type UploadType = 'pdf_convert' | 'question_parse';

// 文件状态
export type FileStatus = 'uploaded' | 'processing' | 'completed' | 'failed';

// 文件上传属性接口
export interface FileUploadAttributes {
  id: number;
  userId: number;
  originalFilename: string;
  filename: string;
  filepath: string;
  mimetype: string;
  fileSize: number;
  uploadType: UploadType;
  status: FileStatus;
  createdAt: Date;
  updatedAt: Date;
}

// 创建文件上传记录时的可选属性
export interface FileUploadCreationAttributes extends Optional<FileUploadAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// FileUpload模型类
class FileUpload extends Model<FileUploadAttributes, FileUploadCreationAttributes> implements FileUploadAttributes {
  public id!: number;
  public userId!: number;
  public originalFilename!: string;
  public filename!: string;
  public filepath!: string;
  public mimetype!: string;
  public fileSize!: number;
  public uploadType!: UploadType;
  public status!: FileStatus;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联的用户
  public User?: User;

  // 更新文件状态
  public async updateStatus(status: FileStatus): Promise<void> {
    this.status = status;
    await this.save();
  }

  // 获取文件大小的可读格式
  public getReadableFileSize(): string {
    const bytes = this.fileSize;
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 获取文件扩展名
  public getFileExtension(): string {
    return this.originalFilename.split('.').pop()?.toLowerCase() || '';
  }

  // 检查是否为图片文件
  public isImageFile(): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.includes(this.getFileExtension());
  }

  // 检查是否为文档文件
  public isDocumentFile(): boolean {
    const documentExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
    return documentExtensions.includes(this.getFileExtension());
  }

  // 获取上传类型的中文名称
  public getUploadTypeName(): string {
    const typeNames = {
      pdf_convert: 'PDF转换',
      question_parse: '试题解析'
    };
    return typeNames[this.uploadType] || '未知类型';
  }

  // 获取状态的中文名称
  public getStatusName(): string {
    const statusNames = {
      uploaded: '已上传',
      processing: '处理中',
      completed: '已完成',
      failed: '失败'
    };
    return statusNames[this.status] || '未知状态';
  }
}

// 定义模型
FileUpload.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  originalFilename: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  filename: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  filepath: {
    type: DataTypes.STRING(500),
    allowNull: false
  },
  mimetype: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  fileSize: {
    type: DataTypes.BIGINT,
    allowNull: false
  },
  uploadType: {
    type: DataTypes.ENUM('pdf_convert', 'question_parse'),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('uploaded', 'processing', 'completed', 'failed'),
    allowNull: false,
    defaultValue: 'uploaded'
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'FileUpload',
  tableName: 'file_uploads',
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['uploadType']
    },
    {
      fields: ['status']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// 定义关联关系
FileUpload.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

User.hasMany(FileUpload, {
  foreignKey: 'userId',
  as: 'fileUploads'
});

export { FileUpload };
export default FileUpload;
