#!/usr/bin/env ts-node

/**
 * 数据库初始化脚本
 * 创建数据库、表结构和初始数据
 */

import dotenv from 'dotenv';
import { connectDatabase, sequelize } from '../config/database';
import { User } from '../models/User';
import { Question, QuestionType } from '../models/Question';
import { FileUpload } from '../models/FileUpload';
import { ProcessingTask } from '../models/ProcessingTask';
import { Exam } from '../models/Exam';
import bcrypt from 'bcryptjs';

// 加载环境变量
dotenv.config();

export async function initDatabase(skipConnection: boolean = false): Promise<void> {
  console.log('🚀 开始初始化数据库...');
  console.log('================================');

  try {
    // 1. 连接数据库（如果需要）
    if (!skipConnection) {
      console.log('📡 连接数据库...');
      await connectDatabase();
    } else {
      console.log('📡 使用现有数据库连接...');
    }

    // 2. 同步数据库表结构
    console.log('🔄 同步数据库表结构...');
    await sequelize.sync({ force: false, alter: true });
    console.log('✅ 数据库表结构同步完成');

    // 3. 检查是否已有数据
    const userCount = await User.count();
    if (userCount > 0) {
      console.log('📊 数据库已有数据，跳过初始化');
      return;
    }

    // 4. 创建管理员用户
    console.log('👤 创建管理员用户...');
    const adminPasswordHash = await bcrypt.hash('admin123456', 10);
    
    await User.create({
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: adminPasswordHash,
      fullName: '系统管理员',
      userType: 'admin',
      isActive: true,
      isEmailVerified: true,
      dailyUsage: {
        pdfConvert: 0,
        questionParse: 0,
        examGenerate: 0
      },
      lastUsageReset: new Date()
    });
    console.log('✅ 管理员用户创建成功');

    // 5. 创建测试用户
    console.log('👥 创建测试用户...');
    const testPasswordHash = await bcrypt.hash('test123456', 10);
    
    const testUser = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: testPasswordHash,
      fullName: '测试用户',
      userType: 'free',
      isActive: true,
      isEmailVerified: true,
      dailyUsage: {
        pdfConvert: 0,
        questionParse: 0,
        examGenerate: 0
      },
      lastUsageReset: new Date()
    });
    console.log('✅ 测试用户创建成功');

    // 6. 创建示例题目
    console.log('📝 创建示例题目...');
    
    const sampleQuestions = [
      {
        userId: testUser.id,
        questionNumber: '1',
        questionType: 'single_choice' as QuestionType,
        subject: '数学',
        difficultyLevel: 3,
        stem: '下列哪个数是质数？',
        options: [
          { key: 'A', content: '4' },
          { key: 'B', content: '6' },
          { key: 'C', content: '7' },
          { key: 'D', content: '8' }
        ],
        correctAnswer: 'C',
        explanation: '质数是只能被1和自身整除的大于1的自然数。7只能被1和7整除，所以是质数。',
        tags: ['数学', '质数', '基础']
      },
      {
        userId: testUser.id,
        questionNumber: '2',
        questionType: 'multiple_choice' as QuestionType,
        subject: '计算机科学',
        difficultyLevel: 4,
        stem: '以下哪些是编程语言？',
        options: [
          { key: 'A', content: 'JavaScript' },
          { key: 'B', content: 'Python' },
          { key: 'C', content: 'HTML' },
          { key: 'D', content: 'Java' }
        ],
        correctAnswer: 'A,B,D',
        explanation: 'JavaScript、Python和Java都是编程语言，而HTML是标记语言。',
        tags: ['计算机', '编程语言']
      },
      {
        userId: testUser.id,
        questionNumber: '3',
        questionType: 'true_false' as QuestionType,
        subject: '物理',
        difficultyLevel: 2,
        stem: '光在真空中的传播速度是3×10^8 m/s。',
        options: [
          { key: 'A', content: '正确' },
          { key: 'B', content: '错误' }
        ],
        correctAnswer: 'A',
        explanation: '光在真空中的传播速度确实是约3×10^8 m/s，这是一个物理常数。',
        tags: ['物理', '光学', '常数']
      }
    ];

    for (const questionData of sampleQuestions) {
      await Question.create(questionData);
    }
    console.log('✅ 示例题目创建成功');

    // 7. 创建示例试卷
    console.log('📋 创建示例试卷...');
    
    await Exam.create({
      userId: testUser.id,
      title: '数学基础测试',
      description: '这是一份数学基础知识测试试卷',
      subject: '数学',
      examType: 'practice',
      totalScore: 100,
      timeLimit: 60,
      instructions: '请仔细阅读题目，选择正确答案。',
      questions: [
        {
          questionId: 1,
          score: 100,
          questionOrder: 1
        }
      ],
      isPublished: false
    });
    console.log('✅ 示例试卷创建成功');

    // 8. 显示初始化结果
    console.log('\n🎉 数据库初始化完成！');
    console.log('================================');
    console.log('📊 初始化统计:');
    console.log(`  用户数量: ${await User.count()}`);
    console.log(`  题目数量: ${await Question.count()}`);
    console.log(`  试卷数量: ${await Exam.count()}`);
    console.log('');
    console.log('👤 默认账户信息:');
    console.log('  管理员账户:');
    console.log('    用户名: admin');
    console.log('    邮箱: <EMAIL>');
    console.log('    密码: admin123456');
    console.log('');
    console.log('  测试账户:');
    console.log('    用户名: testuser');
    console.log('    邮箱: <EMAIL>');
    console.log('    密码: test123456');
    console.log('');
    console.log('🚀 您现在可以启动应用程序了！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  } finally {
    // 只有在不跳过连接时才关闭连接
    if (!skipConnection) {
      await sequelize.close();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase()
    .then(() => {
      console.log('✅ 初始化脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 初始化脚本执行失败:', error);
      process.exit(1);
    });
}
