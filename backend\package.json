{"name": "d2x-backend", "version": "1.0.0", "description": "后端API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "init-db": "ts-node src/scripts/initDatabase.ts", "test-email": "ts-node src/scripts/testEmail.ts", "validate": "ts-node src/scripts/validateProject.ts", "test-models": "ts-node src/scripts/testModels.ts", "validate-all": "ts-node src/scripts/validateAllModels.ts", "check-dns": "ts-node src/scripts/checkDNS.ts", "test-connection": "ts-node src/scripts/testConnection.ts", "test-init": "ts-node src/scripts/testInit.ts", "db:check": "ts-node src/scripts/resetDatabase.ts -- --check", "db:reset": "ts-node src/scripts/resetDatabase.ts -- --reset", "test-login": "ts-node src/scripts/testLogin.ts", "test-question-parsing": "ts-node src/scripts/testQuestionParsing.ts", "test-pandoc": "ts-node src/scripts/testPandoc.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "reflect-metadata": "^0.1.13", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "bull": "^4.12.2", "redis": "^4.6.11", "cheerio": "^1.0.0-rc.12", "jsdom": "^23.0.1", "axios": "^1.6.2", "form-data": "^4.0.0", "uuid": "^9.0.1", "mime-types": "^2.1.35", "nodemailer": "^6.9.7", "pdf-parse": "^1.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/sequelize": "^4.28.18", "@types/uuid": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/cheerio": "^0.22.35", "@types/jsdom": "^21.1.6", "@types/nodemailer": "^6.4.14", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}}