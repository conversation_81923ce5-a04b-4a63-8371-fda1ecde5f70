# 数据库设置指南

## MySQL 安装和配置

### 1. 安装 MySQL

#### Windows
1. 下载 MySQL Installer：https://dev.mysql.com/downloads/installer/
2. 运行安装程序，选择 "Developer Default" 安装类型
3. 设置 root 用户密码
4. 完成安装

#### macOS
```bash
# 使用 Homebrew 安装
brew install mysql

# 启动 MySQL 服务
brew services start mysql

# 设置 root 密码
mysql_secure_installation
```

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装 MySQL
sudo apt install mysql-server

# 运行安全配置
sudo mysql_secure_installation

# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql
```

### 2. 创建数据库

#### 方法一：使用 MySQL 命令行
```bash
# 登录 MySQL
mysql -u root -p

# 执行初始化脚本
source database/init.sql

# 或者直接执行
mysql -u root -p < database/init.sql
```

#### 方法二：使用 MySQL Workbench
1. 打开 MySQL Workbench
2. 连接到本地 MySQL 服务器
3. 打开 `database/init.sql` 文件
4. 执行脚本

### 3. 配置环境变量

复制 `backend/.env.example` 到 `backend/.env`，并修改数据库配置：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=datedu_hw
DB_USER=root
DB_PASSWORD=your_mysql_password
```

### 4. 验证连接

启动后端服务后，检查控制台输出：
```
✅ MySQL database connected successfully
✅ Database tables synchronized
✅ Database initialized successfully
```

## 数据库结构

### 主要表结构

1. **users** - 用户表
   - 存储用户基本信息、权限、使用量统计
   
2. **questions** - 题目表
   - 存储解析后的题目数据
   
3. **file_uploads** - 文件上传表
   - 记录上传的文件信息
   
4. **processing_tasks** - 处理任务表
   - 跟踪文件处理进度和结果
   
5. **exams** - 试卷表
   - 存储组卷信息

### 默认数据

- **管理员账号**：admin / admin123
- **示例题目**：3道数学基础题目

## 常见问题

### 连接失败
1. 检查 MySQL 服务是否启动
2. 验证用户名和密码
3. 确认数据库名称正确
4. 检查防火墙设置

### 权限问题
```sql
-- 创建专用用户（推荐）
CREATE USER 'datedu_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON datedu_hw.* TO 'datedu_user'@'localhost';
FLUSH PRIVILEGES;
```

### 字符编码问题
确保数据库使用 UTF-8 编码：
```sql
ALTER DATABASE datedu_hw CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 备份和恢复

### 备份数据库
```bash
mysqldump -u root -p datedu_hw > backup.sql
```

### 恢复数据库
```bash
mysql -u root -p datedu_hw < backup.sql
```

## 性能优化

### 索引优化
- 主要查询字段已添加索引
- 定期分析查询性能：`EXPLAIN SELECT ...`

### 配置优化
在 MySQL 配置文件中调整：
```ini
[mysqld]
innodb_buffer_pool_size = 1G
max_connections = 200
query_cache_size = 64M
```
