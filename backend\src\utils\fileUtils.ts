import { Response } from 'express';
import fs from 'fs';
import path from 'path';
import mime from 'mime-types';

// 下载文件
export const downloadFile = async (res: Response, filePath: string, filename?: string): Promise<void> => {
  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      res.status(404).json({ error: '文件不存在' });
      return;
    }
    
    // 获取文件信息
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;
    const mimeType = mime.lookup(filePath) || 'application/octet-stream';
    const downloadFilename = filename || path.basename(filePath);
    
    // 设置响应头
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Length', fileSize);
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(downloadFilename)}"`);
    
    // 创建文件流并发送
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    fileStream.on('error', (error) => {
      console.error('File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({ error: '文件读取失败' });
      }
    });
    
  } catch (error) {
    console.error('Download file error:', error);
    if (!res.headersSent) {
      res.status(500).json({ error: '文件下载失败' });
    }
  }
};

// 删除文件
export const deleteFile = (filePath: string): boolean => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Delete file error:', error);
    return false;
  }
};

// 确保目录存在
export const ensureDir = (dirPath: string): void => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// 获取文件大小（格式化）
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取文件扩展名
export const getFileExtension = (filename: string): string => {
  return path.extname(filename).toLowerCase();
};

// 生成唯一文件名
export const generateUniqueFilename = (originalFilename: string): string => {
  const ext = getFileExtension(originalFilename);
  const name = path.basename(originalFilename, ext);
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  
  return `${name}_${timestamp}_${random}${ext}`;
};

// 验证文件类型
export const validateFileType = (filename: string, allowedTypes: string[]): boolean => {
  const ext = getFileExtension(filename);
  return allowedTypes.includes(ext);
};

// 清理临时文件
export const cleanupTempFiles = (tempDir: string, maxAge: number = 24 * 60 * 60 * 1000): void => {
  try {
    if (!fs.existsSync(tempDir)) return;
    
    const files = fs.readdirSync(tempDir);
    const now = Date.now();
    
    files.forEach(file => {
      const filePath = path.join(tempDir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        console.log(`Cleaned up temp file: ${filePath}`);
      }
    });
  } catch (error) {
    console.error('Cleanup temp files error:', error);
  }
};

// 复制文件
export const copyFile = (src: string, dest: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const readStream = fs.createReadStream(src);
    const writeStream = fs.createWriteStream(dest);
    
    readStream.on('error', reject);
    writeStream.on('error', reject);
    writeStream.on('finish', resolve);
    
    readStream.pipe(writeStream);
  });
};

// 移动文件
export const moveFile = async (src: string, dest: string): Promise<void> => {
  try {
    await copyFile(src, dest);
    fs.unlinkSync(src);
  } catch (error) {
    console.error('Move file error:', error);
    throw error;
  }
};

// 读取文件内容
export const readFileContent = (filePath: string, encoding: BufferEncoding = 'utf-8'): string => {
  try {
    return fs.readFileSync(filePath, encoding);
  } catch (error) {
    console.error('Read file content error:', error);
    throw error;
  }
};

// 写入文件内容
export const writeFileContent = (filePath: string, content: string, encoding: BufferEncoding = 'utf-8'): void => {
  try {
    // 确保目录存在
    const dir = path.dirname(filePath);
    ensureDir(dir);
    
    fs.writeFileSync(filePath, content, encoding);
  } catch (error) {
    console.error('Write file content error:', error);
    throw error;
  }
};

// 检查文件是否为图片
export const isImageFile = (filename: string): boolean => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
  const ext = getFileExtension(filename);
  return imageExtensions.includes(ext);
};

// 检查文件是否为文档
export const isDocumentFile = (filename: string): boolean => {
  const documentExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'];
  const ext = getFileExtension(filename);
  return documentExtensions.includes(ext);
};

// 获取文件MIME类型
export const getFileMimeType = (filename: string): string => {
  return mime.lookup(filename) || 'application/octet-stream';
};
