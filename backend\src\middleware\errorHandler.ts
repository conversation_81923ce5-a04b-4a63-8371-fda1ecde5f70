import { Request, Response, NextFunction } from 'express';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let { statusCode = 500, message } = error;
  
  // 开发环境下打印错误堆栈
  if (process.env.NODE_ENV === 'development') {
    console.error('Error:', error);
  }
  
  // MongoDB错误处理
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '数据验证失败';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = '无效的数据格式';
  } else if (error.name === 'MongoServerError' && (error as any).code === 11000) {
    statusCode = 409;
    message = '数据已存在';
  }
  
  // JWT错误处理
  if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '无效的访问令牌';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '访问令牌已过期';
  }
  
  // Multer错误处理
  if (error.name === 'MulterError') {
    statusCode = 400;
    if ((error as any).code === 'LIMIT_FILE_SIZE') {
      message = '文件大小超出限制';
    } else if ((error as any).code === 'LIMIT_FILE_COUNT') {
      message = '文件数量超出限制';
    } else {
      message = '文件上传错误';
    }
  }
  
  const response: any = {
    error: message,
    statusCode
  };
  
  // 开发环境下返回错误堆栈
  if (process.env.NODE_ENV === 'development') {
    response.stack = error.stack;
  }
  
  res.status(statusCode).json(response);
};

export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  res.status(404).json({
    error: '请求的资源不存在',
    path: req.originalUrl,
    method: req.method
  });
};

export const createError = (message: string, statusCode: number = 500): AppError => {
  const error: AppError = new Error(message);
  error.statusCode = statusCode;
  error.isOperational = true;
  return error;
};
