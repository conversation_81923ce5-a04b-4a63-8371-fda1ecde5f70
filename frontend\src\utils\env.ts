/**
 * 环境变量工具函数
 * 统一管理前端环境变量的访问
 */

// 获取API基础URL
export const getApiBaseUrl = (): string => {
  return import.meta.env.VITE_API_BASE_URL || '/api'
}

// 获取应用标题
export const getAppTitle = (): string => {
  return import.meta.env.VITE_APP_TITLE || 'd2x'
}

// 获取应用版本
export const getAppVersion = (): string => {
  return import.meta.env.VITE_APP_VERSION || '1.0.0'
}

// 获取应用描述
export const getAppDescription = (): string => {
  return import.meta.env.VITE_APP_DESCRIPTION || '智能试题解析平台'
}

// 检查是否为开发环境
export const isDevelopment = (): boolean => {
  return import.meta.env.VITE_APP_ENV === 'development'
}

// 检查是否启用开发工具
export const isDevToolsEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_DEVTOOLS === 'true'
}

// 检查是否启用Mock数据
export const isMockEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_MOCK === 'true'
}

// 获取所有环境变量（调试用）
export const getAllEnvVars = (): ImportMetaEnv => {
  return import.meta.env
}

// 打印环境信息（仅开发环境）
export const logEnvInfo = (): void => {
  if (isDevelopment()) {
    console.log('🔧 环境变量信息:')
    console.log('API Base URL:', getApiBaseUrl())
    console.log('App Title:', getAppTitle())
    console.log('App Version:', getAppVersion())
    console.log('Environment:', import.meta.env.VITE_APP_ENV)
    console.log('Dev Tools:', isDevToolsEnabled())
    console.log('Mock Enabled:', isMockEnabled())
  }
}
