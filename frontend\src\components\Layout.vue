<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="logo" @click="$router.push('/dashboard')">
            d2x
          </h1>
        </div>
        
        <div class="header-right">
          <!-- 用户使用情况 -->
          <div class="usage-info" v-if="authStore.user">
            <el-tooltip content="今日使用情况" placement="bottom">
              <div class="usage-item">
                <el-icon><Document /></el-icon>
                <span>{{ getUsageText('pdfConvert') }}</span>
              </div>
            </el-tooltip>
            
            <el-tooltip content="题目解析" placement="bottom">
              <div class="usage-item">
                <el-icon><EditPen /></el-icon>
                <span>{{ getUsageText('questionParse') }}</span>
              </div>
            </el-tooltip>
            
            <el-tooltip content="试卷生成" placement="bottom">
              <div class="usage-item">
                <el-icon><Notebook /></el-icon>
                <span>{{ getUsageText('examGenerate') }}</span>
              </div>
            </el-tooltip>
          </div>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleCommand">
            <div class="user-info">
              <el-avatar :src="authStore.user?.avatarUrl" :size="32">
                {{ authStore.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <span class="username">{{ authStore.user?.username }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="tasks">
                  <el-icon><List /></el-icon>
                  任务中心
                </el-dropdown-item>
                <el-dropdown-item v-if="authStore.user?.userType === 'admin'" command="admin">
                  <el-icon><Setting /></el-icon>
                  用户管理
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>
    
    <!-- 主体内容 -->
    <el-container class="layout-main">
      <!-- 侧边栏 -->
      <el-aside class="layout-sidebar">
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Odometer /></el-icon>
            <span>仪表板</span>
          </el-menu-item>
          
          <el-menu-item index="/pdf-convert">
            <el-icon><Document /></el-icon>
            <span>PDF转Word</span>
          </el-menu-item>
          
          <el-menu-item index="/question-parse">
            <el-icon><EditPen /></el-icon>
            <span>试题解析</span>
          </el-menu-item>
          
          <el-menu-item index="/question-bank">
            <el-icon><Collection /></el-icon>
            <span>题库管理</span>
          </el-menu-item>
          
          <el-menu-item index="/exam-builder">
            <el-icon><Plus /></el-icon>
            <span>智能组卷</span>
          </el-menu-item>
          
          <el-menu-item index="/exam-list">
            <el-icon><Notebook /></el-icon>
            <span>试卷列表</span>
          </el-menu-item>

          <!-- 管理员菜单 -->
          <template v-if="authStore.user?.userType === 'admin'">
            <el-divider />
            <el-sub-menu index="admin">
              <template #title>
                <el-icon><Setting /></el-icon>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/admin/users">
                <el-icon><UserFilled /></el-icon>
                <span>用户管理</span>
              </el-menu-item>
            </el-sub-menu>
          </template>
        </el-menu>
      </el-aside>
      
      <!-- 内容区域 -->
      <el-main class="layout-content">
        <!-- 邮箱验证提醒 -->
        <EmailVerificationAlert />
        <slot />
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  Document,
  EditPen,
  Notebook,
  User,
  List,
  SwitchButton,
  ArrowDown,
  Odometer,
  Collection,
  Plus,
  Setting,
  UserFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import EmailVerificationAlert from './EmailVerificationAlert.vue'

const router = useRouter()
const authStore = useAuthStore()

const getUsageText = (feature: 'pdfConvert' | 'questionParse' | 'examGenerate') => {
  const usage = authStore.getFeatureUsage(feature)
  if (usage.limit === -1) {
    return `${usage.used}`
  }
  return `${usage.used}/${usage.limit}`
}

const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'tasks':
      router.push('/tasks')
      break
    case 'admin':
      router.push('/admin/users')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await authStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  background: #fff;
  border-bottom: 1px solid #e8eaec;
  padding: 0;
  height: 60px !important;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin: 0;
  cursor: pointer;
  transition: color 0.3s;
}

.logo:hover {
  color: #337ecc;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.usage-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #333;
}

.dropdown-icon {
  font-size: 12px;
  color: #999;
}

.layout-main {
  flex: 1;
  overflow: hidden;
}

.layout-sidebar {
  width: 250px !important;
  background: #fff;
  border-right: 1px solid #e8eaec;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.layout-content {
  background: #f5f7fa;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .usage-info {
    display: none;
  }
  
  .layout-sidebar {
    width: 200px !important;
  }
  
  .username {
    display: none;
  }
}

@media (max-width: 480px) {
  .layout-main {
    flex-direction: column;
  }
  
  .layout-sidebar {
    width: 100% !important;
    height: auto;
  }
  
  .sidebar-menu {
    display: flex;
    overflow-x: auto;
  }
  
  .sidebar-menu .el-menu-item {
    flex-shrink: 0;
  }
}
</style>
