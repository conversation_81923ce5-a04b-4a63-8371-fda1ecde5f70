import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';
import { Question } from './Question';

// 试卷类型
export type ExamType = 'practice' | 'test' | 'homework' | 'quiz';

// 试卷题目接口
export interface ExamQuestion {
  questionId: number;
  questionOrder: number;
  score: number;
  sectionTitle?: string;
}

// 试卷属性接口
export interface ExamAttributes {
  id: number;
  userId: number;
  title: string;
  description?: string;
  subject?: string;
  examType: ExamType;
  totalScore: number;
  timeLimit?: number; // 考试时间限制（分钟）
  instructions?: string; // 考试说明
  questions: ExamQuestion[];
  isPublished: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 创建试卷时的可选属性
export interface ExamCreationAttributes extends Optional<ExamAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// Exam模型类
class Exam extends Model<ExamAttributes, ExamCreationAttributes> implements ExamAttributes {
  public id!: number;
  public userId!: number;
  public title!: string;
  public description?: string;
  public subject?: string;
  public examType!: ExamType;
  public totalScore!: number;
  public timeLimit?: number;
  public instructions?: string;
  public questions!: ExamQuestion[];
  public isPublished!: boolean;
  public createdAt!: Date;
  public updatedAt!: Date;

  // 关联的用户
  public User?: User;

  // 添加题目到试卷
  public addQuestion(questionId: number, sectionTitle?: string, score: number = 1): void {
    const questionOrder = this.questions.length + 1;
    this.questions.push({
      questionId,
      questionOrder,
      score,
      sectionTitle
    });
    this.updateTotalScore();
  }

  // 从试卷中移除题目
  public removeQuestion(questionId: number): void {
    this.questions = this.questions.filter(q => q.questionId !== questionId);
    this.reorderQuestions();
    this.updateTotalScore();
  }

  // 重新排序题目
  public reorderQuestions(questionIds?: number[]): void {
    if (questionIds) {
      // 根据提供的ID顺序重新排序
      const reorderedQuestions: ExamQuestion[] = [];
      questionIds.forEach((id, index) => {
        const question = this.questions.find(q => q.questionId === id);
        if (question) {
          question.questionOrder = index + 1;
          reorderedQuestions.push(question);
        }
      });
      this.questions = reorderedQuestions;
    } else {
      // 重新分配序号
      this.questions.forEach((question, index) => {
        question.questionOrder = index + 1;
      });
    }
  }

  // 更新总分
  private updateTotalScore(): void {
    this.totalScore = this.questions.reduce((total, question) => total + question.score, 0);
  }

  // 获取题目数量
  public getQuestionCount(): number {
    return this.questions.length;
  }

  // 获取按大题分组的题目
  public getQuestionsBySection(): Map<string, ExamQuestion[]> {
    const sections = new Map<string, ExamQuestion[]>();
    
    this.questions.forEach(question => {
      const sectionTitle = question.sectionTitle || '题目';
      if (!sections.has(sectionTitle)) {
        sections.set(sectionTitle, []);
      }
      sections.get(sectionTitle)!.push(question);
    });
    
    return sections;
  }

  // 验证试卷数据
  public validateExam(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证标题
    if (!this.title || this.title.trim().length < 2) {
      errors.push('试卷标题至少2个字符');
    }

    // 验证题目
    if (this.questions.length === 0) {
      errors.push('试卷至少需要一道题目');
    }

    // 验证时间限制
    if (this.timeLimit && (this.timeLimit < 1 || this.timeLimit > 600)) {
      errors.push('考试时间限制应在1-600分钟之间');
    }

    // 验证分值
    if (this.totalScore <= 0) {
      errors.push('试卷总分必须大于0');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 获取试卷类型的中文名称
  public getExamTypeName(): string {
    const typeNames = {
      practice: '练习',
      test: '考试',
      homework: '作业',
      quiz: '测验'
    };
    return typeNames[this.examType] || '未知类型';
  }

  // 发布试卷
  public async publish(): Promise<void> {
    const validation = this.validateExam();
    if (!validation.isValid) {
      throw new Error(`试卷验证失败: ${validation.errors.join(', ')}`);
    }
    
    this.isPublished = true;
    await this.save();
  }

  // 取消发布
  public async unpublish(): Promise<void> {
    this.isPublished = false;
    await this.save();
  }
}

// 定义模型
Exam.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [2, 255]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  subject: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  examType: {
    type: DataTypes.ENUM('practice', 'test', 'homework', 'quiz'),
    allowNull: false,
    defaultValue: 'practice'
  },
  totalScore: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  timeLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1,
      max: 600
    }
  },
  instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  questions: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: []
  },
  isPublished: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  createdAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  sequelize,
  modelName: 'Exam',
  tableName: 'exams',
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['examType']
    },
    {
      fields: ['subject']
    },
    {
      fields: ['isPublished']
    },
    {
      fields: ['createdAt']
    }
  ]
});

// 定义关联关系
Exam.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

User.hasMany(Exam, {
  foreignKey: 'userId',
  as: 'exams'
});

export { Exam };
export default Exam;
