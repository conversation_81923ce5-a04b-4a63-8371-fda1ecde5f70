import * as cheerio from 'cheerio';

export class QuestionParser {
  
  // 从HTML解析题目
  async parseQuestionsFromHtml(
    htmlContent: string,
    userId: number,
    sourceFileId: number
  ): Promise<any[]> {
    const questions: any[] = [];

    console.log('🧠 开始解析HTML内容，长度:', htmlContent.length);

    try {
      const $ = cheerio.load(htmlContent);
      // 获取文本内容
      const text = $('body').text() || $.html();
      console.log('📝 提取的文本长度:', text.length);

      const lines = text.split('\n').filter((line: string) => line.trim());
      console.log('📄 有效行数:', lines.length);

      let questionCount = 0;

      for (let i = 0; i < lines.length; i++) {
        const trimmedLine = lines[i].trim();

        // 更灵活的题号匹配
        const questionMatch = trimmedLine.match(/^(\d+)[\.、\s\)）]/);
        if (questionMatch) {
          const questionNumber = questionMatch[1];
          questionCount++;

          // 获取题目内容（可能跨多行）
          let stem = trimmedLine;
          let options: string[] = [];

          // 查找选项（A、B、C、D等）
          for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
            const nextLine = lines[j].trim();
            const optionMatch = nextLine.match(/^[A-Z][\.、\s\)）]/);
            if (optionMatch) {
              options.push(nextLine);
            } else if (nextLine && !nextLine.match(/^\d+[\.、\s\)）]/)) {
              // 如果不是新题目，可能是题目内容的延续
              if (options.length === 0) {
                stem += ' ' + nextLine;
              }
            } else {
              break;
            }
          }

          // 创建题目对象
          const question = {
            userId,
            sourceFileId,
            questionNumber,
            questionType: options.length > 0 ? 'single_choice' : 'essay',
            subject: '未分类',
            difficultyLevel: 3,
            stem: stem,
            options: options,
            correctAnswer: '',
            explanation: '',
            tags: []
          };

          questions.push(question);
          console.log(`✅ 解析题目 ${questionNumber}:`, {
            stem: stem.substring(0, 50) + '...',
            optionsCount: options.length
          });
        }
      }

      console.log('📊 解析完成，共找到题目:', questionCount);

    } catch (error) {
      console.error('❌ 题目解析失败:', error);
    }

    // 如果没有解析到题目，创建一个示例题目
    if (questions.length === 0) {
      console.log('⚠️ 未解析到题目，创建示例题目');
      questions.push({
        userId,
        sourceFileId,
        questionNumber: '1',
        questionType: 'essay',
        subject: '未分类',
        difficultyLevel: 3,
        stem: '从上传的文件中未能自动识别出标准格式的题目。请检查文件格式是否正确，或手动编辑题目内容。',
        options: [],
        correctAnswer: '',
        explanation: '这是一个示例题目，表示文件解析过程已完成，但未能识别出标准题目格式。',
        tags: ['示例', '解析失败']
      });
    }

    return questions;
  }
}
