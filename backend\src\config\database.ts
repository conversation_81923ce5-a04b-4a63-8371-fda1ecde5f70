import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();

// 强制使用IP地址，避免DNS解析问题
const dbHost = process.env.DB_HOST || '127.0.0.1';
console.log(`🔧 数据库配置 - 主机: ${dbHost}`);

const sequelize = new Sequelize({
  host: dbHost,
  port: parseInt(process.env.DB_PORT || '3306'),
  database: process.env.DB_NAME || 'd2x_db',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  dialectOptions: {
    connectTimeout: 60000,
    charset: 'utf8mb4',
    timezone: '+08:00'
  },
  define: {
    timestamps: true,
    underscored: false,
    freezeTableName: true,
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci'
  },
  retry: {
    match: [
      /ETIMEDOUT/,
      /EHOSTUNREACH/,
      /ECONNRESET/,
      /ECONNREFUSED/,
      /TIMEOUT/,
      /ESOCKETTIMEDOUT/,
      /EHOSTUNREACH/,
      /EPIPE/,
      /EAI_AGAIN/,
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/
    ],
    max: 3
  }
});

export const connectDatabase = async (): Promise<void> => {
  try {
    // 显示实际连接信息
    console.log('🔍 尝试连接MySQL数据库...');
    console.log(`配置的主机: ${process.env.DB_HOST}`);
    console.log(`配置的端口: ${process.env.DB_PORT}`);
    console.log(`配置的数据库: ${process.env.DB_NAME}`);
    console.log(`配置的用户: ${process.env.DB_USER}`);

    await sequelize.authenticate();
    console.log('✅ MySQL database connected successfully');

    // 简单的连接验证
    try {
      await sequelize.query('SELECT 1 as test');
      console.log('📊 数据库连接验证成功');
    } catch (queryError) {
      console.log('⚠️  连接验证跳过，但基础连接正常');
    }

    // 同步数据库表结构
    if (process.env.NODE_ENV === 'development') {
      try {
        // 使用更安全的同步方式
        await sequelize.sync({ force: false });
        console.log('✅ Database tables synchronized');
      } catch (syncError: any) {
        console.warn('⚠️ 数据库同步遇到问题，尝试手动处理...');
        console.warn('错误信息:', syncError.message);

        // 如果是索引过多的问题，跳过同步
        if (syncError.message.includes('Too many keys') || syncError.message.includes('ER_TOO_MANY_KEYS')) {
          console.log('📝 检测到索引过多问题，跳过自动同步');
          console.log('💡 建议手动清理数据库索引或重新创建表');
        } else {
          throw syncError;
        }
      }
    }
  } catch (error: any) {
    console.error('❌ Unable to connect to MySQL database:', error);
    console.error('配置信息:');
    console.error(`  主机: ${process.env.DB_HOST}`);
    console.error(`  端口: ${process.env.DB_PORT}`);
    console.error(`  数据库: ${process.env.DB_NAME}`);
    console.error(`  用户: ${process.env.DB_USER}`);

    throw error;
  }
};

export { sequelize };
