import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import FormData from 'form-data';
import { ProcessingTask } from '../models/ProcessingTask';
import { FileUpload } from '../models/FileUpload';
import { Question } from '../models/Question';
import { QuestionParser } from './QuestionParser';

const execAsync = promisify(exec);

export class DocumentProcessor {
  private questionParser = new QuestionParser();
  
  // PDF转Word处理
  async processPdfToWord(taskId: string): Promise<void> {
    const task = await ProcessingTask.findByPk(taskId, {
      include: [{ model: FileUpload, as: 'fileUpload' }]
    });
    if (!task) {
      throw new Error('任务不存在');
    }
    
    const fileUpload = (task as any).fileUpload;
    if (!fileUpload) {
      throw new Error('文件不存在');
    }
    
    try {
      // 更新任务状态
      task.updateProgress(10, 'processing');
      await task.save();
      
      // 验证文件路径
      const inputPath = fileUpload.filepath;
      if (!inputPath) {
        throw new Error('文件路径不存在');
      }

      // 检查文件是否存在
      if (!fs.existsSync(inputPath)) {
        throw new Error(`文件不存在: ${inputPath}`);
      }

      const outputDir = path.dirname(inputPath);
      const outputFilename = `${path.parse(fileUpload.filename).name}.docx`;
      const outputPath = path.join(outputDir, outputFilename);
      
      // 检查是否使用高级版（Mathpix）
      const useAdvanced = this.shouldUseAdvancedMode(fileUpload);
      
      if (useAdvanced) {
        await this.convertPdfToWordAdvanced(inputPath, outputPath, task);
      } else {
        await this.convertPdfToWordBasic(inputPath, outputPath, task);
      }
      
      // 更新任务结果
      task.markCompleted({
        outputPath,
        outputFilename,
        conversionType: useAdvanced ? 'advanced' : 'basic'
      });
      
      // 更新文件状态
      fileUpload.status = 'completed';
      await fileUpload.save();
      await task.save();
      
    } catch (error) {
      console.error('PDF转Word处理失败:', error);
      task.markFailed(error instanceof Error ? error.message : '处理失败');
      
      fileUpload.status = 'failed';
      await fileUpload.save();
      await task.save();
    }
  }
  
  // 基础版PDF转Word（使用Node.js PDF解析）
  private async convertPdfToWordBasic(inputPath: string, outputPath: string, task: ProcessingTask): Promise<void> {
    task.updateProgress(30);
    await task.save();

    try {
      // 第一步：提取PDF文本
      console.log('📄 开始提取PDF文本内容...');
      const textContent = await this.extractPdfText(inputPath);

      task.updateProgress(60);
      await task.save();

      // 第二步：创建Word文档内容
      console.log('📝 生成Word文档...');
      await this.createWordDocument(textContent, outputPath);

      task.updateProgress(90);
      await task.save();

      console.log('✅ 基础版PDF转Word完成');

    } catch (error) {
      console.error('❌ 基础版PDF转换失败:', error);
      throw new Error(`基础版PDF转换失败: ${error}`);
    }
  }
  
  // 高级版PDF转Word（使用Mathpix + Pandoc）
  private async convertPdfToWordAdvanced(inputPath: string, outputPath: string, task: ProcessingTask): Promise<void> {
    task.updateProgress(20);
    await task.save();
    
    // 调用Mathpix API
    const mathpixResult = await this.callMathpixAPI(inputPath);
    
    task.updateProgress(60);
    await task.save();
    
    // 将Mathpix结果转换为HTML
    const htmlPath = outputPath.replace('.docx', '.html');
    fs.writeFileSync(htmlPath, mathpixResult.html);
    
    task.updateProgress(70);
    await task.save();
    
    // 使用Pandoc将HTML转换为Word
    const command = `pandoc "${htmlPath}" -o "${outputPath}"`;
    
    try {
      await execAsync(command);
      
      // 清理临时HTML文件
      fs.unlinkSync(htmlPath);
      
      task.updateProgress(90);
      await task.save();
    } catch (error) {
      throw new Error(`Pandoc HTML转Word失败: ${error}`);
    }
  }
  
  // 试题解析处理
  async processQuestionParsing(taskId: string): Promise<void> {
    console.log('🔍 开始处理试题解析任务:', taskId);

    const task = await ProcessingTask.findByPk(taskId, {
      include: [{ model: FileUpload, as: 'fileUpload' }]
    });
    if (!task) {
      console.error('❌ 任务不存在:', taskId);
      throw new Error('任务不存在');
    }

    const fileUpload = (task as any).fileUpload;
    if (!fileUpload) {
      console.error('❌ 文件不存在，任务ID:', taskId);
      throw new Error('文件不存在');
    }

    console.log('📁 处理文件:', {
      filename: fileUpload.originalFilename,
      mimetype: fileUpload.mimetype,
      filepath: fileUpload.filepath
    });

    try {
      // 更新任务状态
      console.log('📝 更新任务状态为processing...');
      task.updateProgress(10, 'processing');
      await task.save();
      
      let htmlContent: string;

      // 根据文件类型选择处理方式
      console.log('🔄 根据文件类型选择处理方式:', fileUpload.mimetype);

      if (fileUpload.mimetype === 'application/pdf') {
        console.log('📄 处理PDF文件...');
        try {
          // 首先尝试使用Mathpix API
          console.log('🔄 尝试使用Mathpix API...');
          const mathpixResult = await this.callMathpixAPI(fileUpload.filepath);
          htmlContent = mathpixResult.html;
          console.log('✅ Mathpix API成功，内容长度:', htmlContent.length);
        } catch (mathpixError: any) {
          console.warn('⚠️ Mathpix API失败，使用基础PDF解析:', mathpixError?.message || mathpixError);
          // 备用方案：使用基础PDF解析
          htmlContent = await this.extractPdfTextAsHtml(fileUpload.filepath);
          console.log('✅ 基础PDF解析完成，内容长度:', htmlContent.length);
        }

        task.updateProgress(40);
        await task.save();
      } else {
        console.log('📝 处理Word文件，使用Pandoc...');
        // Word文件使用Pandoc处理
        htmlContent = await this.convertWordToHtml(fileUpload.filepath);
        console.log('✅ Word转HTML完成，内容长度:', htmlContent.length);

        task.updateProgress(40);
        await task.save();
      }
      
      // 解析HTML内容提取题目
      console.log('🧠 开始解析HTML内容提取题目...');
      const questions = await this.questionParser.parseQuestionsFromHtml(
        htmlContent,
        fileUpload.userId,
        fileUpload.id
      );

      console.log('📊 解析结果:', {
        questionsCount: questions.length,
        sampleQuestion: questions[0] ? {
          questionNumber: questions[0].questionNumber,
          stem: questions[0].stem.substring(0, 50) + '...'
        } : null
      });

      task.updateProgress(80);
      await task.save();

      // 保存题目到数据库
      console.log('💾 保存题目到数据库...');
      const savedQuestions = await Question.bulkCreate(questions, { returning: true });
      console.log('✅ 成功保存题目数量:', savedQuestions.length);
      
      // 更新任务结果
      task.markCompleted({
        questionsCount: savedQuestions.length,
        questions: savedQuestions.map((q: any) => ({
          id: q.id,
          questionNumber: q.questionNumber,
          questionType: q.questionType,
          stem: q.stem.substring(0, 100) + '...'
        }))
      });
      
      // 更新文件状态
      fileUpload.status = 'completed';
      await fileUpload.save();
      await task.save();
      
    } catch (error) {
      console.error('试题解析处理失败:', error);
      task.markFailed(error instanceof Error ? error.message : '解析失败');
      
      fileUpload.status = 'failed';
      await fileUpload.save();
      await task.save();
    }
  }
  
  // 调用Mathpix API
  private async callMathpixAPI(filePath: string): Promise<{ html: string; confidence: number }> {
    const appId = process.env.MATHPIX_APP_ID;
    const appKey = process.env.MATHPIX_APP_KEY;
    
    if (!appId || !appKey) {
      throw new Error('Mathpix API配置缺失');
    }
    
    const formData = new FormData();
    formData.append('file', fs.createReadStream(filePath));
    formData.append('options_json', JSON.stringify({
      math_inline_delimiters: ['$', '$'],
      math_display_delimiters: ['$$', '$$'],
      rm_spaces: true
    }));
    
    try {
      const response = await axios.post('https://api.mathpix.com/v3/pdf', formData, {
        headers: {
          ...formData.getHeaders(),
          'app_id': appId,
          'app_key': appKey
        },
        timeout: 60000 // 60秒超时
      });
      
      return {
        html: response.data.html,
        confidence: response.data.confidence || 0.8
      };
    } catch (error) {
      console.error('Mathpix API调用失败:', error);
      throw new Error('Mathpix API调用失败');
    }
  }
  
  // Word转HTML
  private async convertWordToHtml(inputPath: string): Promise<string> {
    console.log('📝 开始Word转HTML转换...');
    console.log('📁 输入文件:', inputPath);

    // 检查输入文件是否存在
    if (!fs.existsSync(inputPath)) {
      throw new Error(`输入文件不存在: ${inputPath}`);
    }

    const fileStats = fs.statSync(inputPath);
    console.log('📊 文件信息:', {
      size: fileStats.size,
      modified: fileStats.mtime
    });

    const outputPath = inputPath.replace(/\.(docx?|doc)$/i, '.html');
    console.log('📄 输出文件:', outputPath);

    // 构建Pandoc命令，添加更多选项
    const command = `pandoc "${inputPath}" -t html --extract-media=temp -o "${outputPath}"`;
    console.log('🔧 执行命令:', command);

    try {
      // 执行Pandoc转换
      const { stdout, stderr } = await execAsync(command);

      if (stderr) {
        console.warn('⚠️ Pandoc警告:', stderr);
      }

      if (stdout) {
        console.log('📝 Pandoc输出:', stdout);
      }

      // 检查输出文件是否生成
      if (!fs.existsSync(outputPath)) {
        throw new Error('Pandoc转换完成但未生成输出文件');
      }

      // 读取HTML内容
      const htmlContent = fs.readFileSync(outputPath, 'utf-8');
      console.log('📄 HTML内容长度:', htmlContent.length);

      // 如果内容为空或太短，记录警告
      if (htmlContent.length < 50) {
        console.warn('⚠️ HTML内容可能为空或过短:', htmlContent);
      }

      // 清理临时HTML文件
      try {
        fs.unlinkSync(outputPath);
        console.log('🗑️ 清理临时文件完成');
      } catch (cleanupError) {
        console.warn('⚠️ 清理临时文件失败:', cleanupError);
      }

      return htmlContent;

    } catch (error: any) {
      console.error('❌ Word转HTML失败:', error);

      // 尝试备用方案：直接读取文件内容
      console.log('🔄 尝试备用方案...');
      return await this.extractWordContentAsHtml(inputPath);
    }
  }
  
  // 判断是否使用高级模式
  private shouldUseAdvancedMode(_fileUpload: any): boolean {
    // 暂时禁用高级模式，所有用户都使用基础版
    // TODO: 后续可以根据用户类型、文件大小等因素决定
    return false;

    // 原逻辑（已注释）：
    // 需要先获取用户信息才能判断用户类型
    // const user = await User.findByPk(fileUpload.userId);
    // return user?.userType !== 'free' && fileUpload.fileSize > 5 * 1024 * 1024;
  }

  // 提取PDF文本内容
  private async extractPdfText(inputPath: string): Promise<string> {
    try {
      // 尝试使用pdf-parse包解析PDF
      const pdfParse = require('pdf-parse');
      const pdfBuffer = fs.readFileSync(inputPath);

      const data = await pdfParse(pdfBuffer);
      return data.text || '无法提取文本内容';

    } catch (error) {
      console.warn('pdf-parse解析失败，使用备用方案:', error);

      // 备用方案：返回提示信息
      const fileName = path.basename(inputPath);
      return `PDF文件: ${fileName}\n\n抱歉，基础版无法完全解析此PDF文件的内容。\n\n建议：\n1. 使用高级版功能获得更好的解析效果\n2. 手动复制PDF内容到Word文档\n3. 尝试使用其他PDF转换工具\n\n如需帮助，请联系技术支持。`;
    }
  }

  // 创建Word文档
  private async createWordDocument(textContent: string, outputPath: string): Promise<void> {
    try {
      // 创建一个简单的HTML文档，然后用Pandoc转换为Word
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF转换结果</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        .header { border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
        .content { white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="header">
        <h1>PDF转Word转换结果</h1>
        <p>转换时间: ${new Date().toLocaleString('zh-CN')}</p>
    </div>
    <div class="content">${this.escapeHtml(textContent)}</div>
</body>
</html>`;

      // 创建临时HTML文件
      const htmlPath = outputPath.replace('.docx', '.html');
      fs.writeFileSync(htmlPath, htmlContent, 'utf-8');

      // 使用Pandoc将HTML转换为Word
      const pandocCommand = `pandoc "${htmlPath}" -o "${outputPath}"`;
      await execAsync(pandocCommand);

      // 清理临时HTML文件
      if (fs.existsSync(htmlPath)) {
        fs.unlinkSync(htmlPath);
      }

    } catch (error) {
      // 如果Pandoc不可用，创建一个简单的文本文件作为备用
      console.warn('Pandoc不可用，创建文本文件作为备用');
      const txtPath = outputPath.replace('.docx', '.txt');
      fs.writeFileSync(txtPath, textContent, 'utf-8');

      // 重命名为.docx（虽然实际是文本文件）
      fs.renameSync(txtPath, outputPath);
    }
  }

  // HTML转义函数
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  // 基础PDF文本提取并转换为HTML
  private async extractPdfTextAsHtml(inputPath: string): Promise<string> {
    try {
      console.log('📄 开始基础PDF文本提取...');
      const textContent = await this.extractPdfText(inputPath);

      // 将文本转换为简单的HTML格式
      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF内容</title>
</head>
<body>
    <div class="content">
        ${this.escapeHtml(textContent).replace(/\n/g, '<br>')}
    </div>
</body>
</html>`;

      console.log('✅ PDF文本转HTML完成');
      return htmlContent;
    } catch (error) {
      console.error('❌ 基础PDF解析失败:', error);
      // 返回一个包含错误信息的HTML
      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF解析失败</title>
</head>
<body>
    <div class="error">
        <p>PDF文件解析失败，无法提取文本内容。</p>
        <p>建议：请确保PDF文件格式正确，或尝试使用其他格式的文件。</p>
    </div>
</body>
</html>`;
    }
  }

  // Word内容提取备用方案
  private async extractWordContentAsHtml(inputPath: string): Promise<string> {
    console.log('📝 使用备用方案提取Word内容...');

    try {
      // 尝试使用简单的Pandoc命令
      const simpleCommand = `pandoc "${inputPath}" -t html`;
      console.log('🔧 执行简化命令:', simpleCommand);

      const { stdout, stderr } = await execAsync(simpleCommand);

      if (stderr) {
        console.warn('⚠️ 简化命令警告:', stderr);
      }

      if (stdout && stdout.trim().length > 0) {
        console.log('✅ 简化命令成功，内容长度:', stdout.length);
        return stdout;
      }

      throw new Error('简化命令也未能提取内容');

    } catch (error) {
      console.error('❌ 备用方案也失败:', error);

      // 最后的备用方案：生成错误提示HTML
      const fileName = path.basename(inputPath);
      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Word文档解析失败</title>
</head>
<body>
    <div class="error">
        <h2>Word文档解析失败</h2>
        <p><strong>文件名:</strong> ${fileName}</p>
        <p><strong>问题:</strong> 无法使用Pandoc转换Word文档为HTML格式</p>
        <p><strong>可能原因:</strong></p>
        <ul>
            <li>Pandoc未正确安装或配置</li>
            <li>Word文档格式不兼容</li>
            <li>文件损坏或加密</li>
        </ul>
        <p><strong>建议:</strong></p>
        <ul>
            <li>检查Pandoc安装: <code>pandoc --version</code></li>
            <li>尝试使用标准的.docx格式</li>
            <li>确保文件未加密或损坏</li>
            <li>手动复制内容到新文档后重新上传</li>
        </ul>
    </div>
</body>
</html>`;
    }
  }
}
