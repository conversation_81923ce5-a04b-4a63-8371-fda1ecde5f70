/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_ENV: string
  readonly VITE_ENABLE_MOCK: string
  readonly VITE_ENABLE_DEVTOOLS: string
  readonly VITE_BAIDU_ANALYTICS_ID?: string
  readonly VITE_GA_TRACKING_ID?: string
  readonly VITE_CUSTOMER_SERVICE_URL?: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
