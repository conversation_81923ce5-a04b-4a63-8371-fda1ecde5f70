{"name": "datedu-hw-d2x", "version": "1.0.0", "description": "面向教育工作者的文档转换与智能试题解析SaaS平台", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "check-email": "node check-email-config.js", "test-email": "cd backend && npm run test-email"}, "keywords": ["education", "document-conversion", "question-parsing", "exam-generation", "saas"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}