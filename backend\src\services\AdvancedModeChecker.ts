/**
 * 高级模式检查器
 * 用于判断是否应该使用高级版PDF转换功能
 */

import { User } from '../models/User';
import { FileUpload } from '../models/FileUpload';

export class AdvancedModeChecker {
  
  /**
   * 检查是否应该使用高级模式
   */
  static async shouldUseAdvancedMode(fileUpload: FileUpload): Promise<boolean> {
    try {
      // 1. 获取用户信息
      const user = await User.findByPk(fileUpload.userId);
      if (!user) {
        return false; // 用户不存在，使用基础版
      }

      // 2. 检查用户类型
      if (user.userType === 'free') {
        return false; // 免费用户只能使用基础版
      }

      // 3. 检查环境变量配置
      const advancedEnabled = process.env.ENABLE_ADVANCED_PDF_CONVERT === 'true';
      if (!advancedEnabled) {
        return false; // 高级功能未启用
      }

      // 4. 检查API配置
      const hasMathpixConfig = !!(process.env.MATHPIX_APP_ID && process.env.MATHPIX_APP_KEY);
      if (!hasMathpixConfig) {
        console.warn('⚠️ Mathpix API未配置，使用基础版PDF转换');
        return false;
      }

      // 5. 检查文件大小（大文件可能需要高级处理）
      const fileSizeThreshold = 10 * 1024 * 1024; // 10MB
      const isLargeFile = fileUpload.fileSize > fileSizeThreshold;

      // 6. 检查文件类型
      const isPdf = fileUpload.mimetype === 'application/pdf';
      if (!isPdf) {
        return false; // 只有PDF文件才考虑使用高级模式
      }

      // 7. 最终决策
      // 付费用户 + 大文件 + PDF格式 = 使用高级模式
      return user.userType !== 'free' && isLargeFile;

    } catch (error) {
      console.error('检查高级模式时出错:', error);
      return false; // 出错时默认使用基础版
    }
  }

  /**
   * 获取模式选择的原因说明
   */
  static async getModeReason(fileUpload: FileUpload): Promise<string> {
    try {
      const user = await User.findByPk(fileUpload.userId);
      const useAdvanced = await this.shouldUseAdvancedMode(fileUpload);

      if (!user) {
        return '用户不存在，使用基础版';
      }

      if (user.userType === 'free') {
        return '免费用户，使用基础版';
      }

      if (process.env.ENABLE_ADVANCED_PDF_CONVERT !== 'true') {
        return '高级功能未启用，使用基础版';
      }

      if (!(process.env.MATHPIX_APP_ID && process.env.MATHPIX_APP_KEY)) {
        return 'Mathpix API未配置，使用基础版';
      }

      if (fileUpload.mimetype !== 'application/pdf') {
        return '非PDF文件，使用基础版';
      }

      const fileSizeThreshold = 10 * 1024 * 1024;
      const isLargeFile = fileUpload.fileSize > fileSizeThreshold;

      if (useAdvanced) {
        return `付费用户 + 大文件(${Math.round(fileUpload.fileSize / 1024 / 1024)}MB)，使用高级版`;
      } else {
        return `付费用户 + 小文件(${Math.round(fileUpload.fileSize / 1024 / 1024)}MB)，使用基础版`;
      }

    } catch (error) {
      return '检查出错，使用基础版';
    }
  }

  /**
   * 检查高级模式的前置条件
   */
  static checkAdvancedModeRequirements(): {
    enabled: boolean;
    reasons: string[];
  } {
    const reasons: string[] = [];
    let enabled = true;

    // 检查环境变量
    if (process.env.ENABLE_ADVANCED_PDF_CONVERT !== 'true') {
      enabled = false;
      reasons.push('环境变量 ENABLE_ADVANCED_PDF_CONVERT 未设置为 true');
    }

    // 检查Mathpix配置
    if (!process.env.MATHPIX_APP_ID) {
      enabled = false;
      reasons.push('缺少 MATHPIX_APP_ID 配置');
    }

    if (!process.env.MATHPIX_APP_KEY) {
      enabled = false;
      reasons.push('缺少 MATHPIX_APP_KEY 配置');
    }

    // 检查Pandoc（可选）
    // 这里可以添加Pandoc可用性检查

    if (enabled) {
      reasons.push('所有前置条件满足，高级模式可用');
    }

    return { enabled, reasons };
  }
}
