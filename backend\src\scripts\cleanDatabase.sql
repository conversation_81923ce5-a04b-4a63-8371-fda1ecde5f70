-- 数据库清理脚本
-- 解决"Too many keys specified; max 64 keys allowed"问题

-- 1. 查看当前数据库中的所有表
SHOW TABLES;

-- 2. 查看users表的索引
SHOW INDEX FROM users;

-- 3. 删除可能重复或不必要的索引
-- 注意：执行前请备份数据！

-- 删除users表的所有非主键索引（保留数据）
-- ALTER TABLE users DROP INDEX index_name;

-- 4. 如果问题严重，可以重新创建表结构
-- 备份数据
-- CREATE TABLE users_backup AS SELECT * FROM users;

-- 删除原表
-- DROP TABLE users;

-- 5. 重新创建users表（让Sequelize自动创建）
-- 这样可以确保索引结构是最新的

-- 6. 恢复数据（如果需要）
-- INSERT INTO users SELECT * FROM users_backup;

-- 7. 清理备份表
-- DROP TABLE users_backup;

-- 查看所有表的索引数量
SELECT 
    TABLE_NAME,
    COUNT(*) as INDEX_COUNT
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
GROUP BY 
    TABLE_NAME
ORDER BY 
    INDEX_COUNT DESC;

-- 查看具体的索引信息
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'users'
ORDER BY 
    TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
