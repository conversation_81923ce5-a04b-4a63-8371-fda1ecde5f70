# 📦 PDF转换功能依赖安装指南

## 🚨 当前问题
基础版PDF转Word功能需要安装额外的依赖包。

## ✅ 立即解决方案

### 1. 安装Node.js依赖
```bash
cd backend
npm install pdf-parse
```

### 2. 重启应用程序
```bash
npm run dev
```

## 🔧 可选：安装系统工具（提升效果）

### Windows系统

#### 安装Pandoc
1. 访问 https://pandoc.org/installing.html
2. 下载Windows安装包
3. 运行安装程序
4. 重启命令行

#### 安装Poppler（可选）
1. 下载 https://github.com/oschwartz10612/poppler-windows/releases
2. 解压到 `C:\poppler`
3. 添加 `C:\poppler\bin` 到系统PATH
4. 重启命令行

### Linux系统
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install pandoc poppler-utils

# CentOS/RHEL
sudo yum install pandoc poppler-utils
```

### macOS系统
```bash
# 使用Homebrew
brew install pandoc poppler
```

## 📊 功能对比

| 工具 | 状态 | 功能 | 必需性 |
|------|------|------|--------|
| pdf-parse | ✅ 已配置 | PDF文本提取 | 必需 |
| Pandoc | 🔧 可选 | HTML转Word | 推荐 |
| Poppler | 🔧 可选 | 高质量PDF解析 | 可选 |

## 🎯 当前功能状态

### 基础版PDF转Word流程
1. **PDF文本提取**: 使用`pdf-parse`包
2. **HTML生成**: 创建格式化的HTML内容
3. **Word转换**: 使用Pandoc转换为Word文档
4. **备用方案**: 如果Pandoc不可用，生成文本文件

### 错误处理机制
- ✅ pdf-parse失败 → 生成提示文档
- ✅ Pandoc不可用 → 生成文本文件
- ✅ 完整的错误日志记录

## 🧪 测试安装

### 1. 测试pdf-parse
```bash
cd backend
node -e "console.log(require('pdf-parse'))"
```

### 2. 测试Pandoc
```bash
pandoc --version
```

### 3. 测试完整流程
上传一个PDF文件测试转换功能。

## 🔍 故障排除

### 问题1: pdf-parse安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 重新安装
npm install pdf-parse --save
```

### 问题2: Pandoc命令不存在
```bash
# Windows: 检查PATH环境变量
echo $env:PATH

# Linux/Mac: 检查PATH
echo $PATH

# 手动指定Pandoc路径（在代码中）
const pandocPath = 'C:\\Program Files\\Pandoc\\pandoc.exe';
```

### 问题3: 权限问题
```bash
# Linux/Mac: 给予执行权限
sudo chmod +x /usr/local/bin/pandoc
```

## 🎉 安装完成验证

安装完成后，应该看到：

1. ✅ `npm install pdf-parse` 成功
2. ✅ PDF上传不再报错
3. ✅ 生成的Word文档可以正常打开
4. ✅ 控制台显示处理进度日志

## 📝 注意事项

### 生产环境
- 确保所有依赖都已安装
- 考虑使用Docker容器化部署
- 监控PDF处理的性能和错误率

### 开发环境
- 可以只安装pdf-parse进行基础测试
- Pandoc可以后续安装以获得更好效果

### 用户体验
- 基础版会在文档中说明转换限制
- 建议用户升级到高级版获得更好效果
- 提供清晰的错误提示和解决建议
