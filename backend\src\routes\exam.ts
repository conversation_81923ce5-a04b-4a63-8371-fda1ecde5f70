import { Router } from 'express';
import { ExamController } from '../controllers/ExamController';
import { authenticateToken, checkFeatureUsage } from '../middleware/auth';

const router = Router();
const examController = new ExamController();

// 获取试卷列表
router.get('/', authenticateToken, examController.getExams.bind(examController));

// 获取试卷详情
router.get('/:examId', authenticateToken, examController.getExamById.bind(examController));

// 创建试卷
router.post('/', authenticateToken, examController.createExam.bind(examController));

// 更新试卷
router.put('/:examId', authenticateToken, examController.updateExam.bind(examController));

// 删除试卷
router.delete('/:examId', authenticateToken, examController.deleteExam.bind(examController));

// 向试卷添加题目
router.post('/:examId/questions', authenticateToken, examController.addQuestionToExam.bind(examController));

// 从试卷移除题目
router.delete('/:examId/questions/:questionId', authenticateToken, examController.removeQuestionFromExam.bind(examController));

// 调整试卷题目顺序
router.put('/:examId/questions/reorder', authenticateToken, examController.reorderExamQuestions.bind(examController));

// 生成试卷Word文档
router.post('/:examId/generate',
  authenticateToken,
  checkFeatureUsage('examGenerate'),
  examController.generateExamDocument.bind(examController)
);

// 预览试卷
router.get('/:examId/preview', authenticateToken, examController.previewExam.bind(examController));

// 复制试卷
router.post('/:examId/duplicate', authenticateToken, examController.duplicateExam.bind(examController));

export default router;
